"""
主训练脚本
包含训练管理器、训练主循环、模型评估、结果保存等功能
"""

import os
import time
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict, List, Optional

from config import Config
from environment import EdgeInferenceEnvironment
from maddpg_algorithm import MADDPGAlgorithm

class TrainingManager:
    """训练管理器"""
    
    def __init__(self, config: Config, device: str = 'cpu'):
        self.config = config
        self.device = device
        
        # 创建环境和算法
        self.env = EdgeInferenceEnvironment(config)
        self.maddpg = MADDPGAlgorithm(config, device)
        
        # 训练统计
        self.training_history = {
            'episode_rewards': [],
            'system_metrics': [],
            'constraint_violations': [],
            'battery_levels': [],
            'actor_losses': [],
            'critic_losses': [],
            'noise_levels': []
        }
        
        # 创建结果目录
        self.results_dir = "training_results"
        os.makedirs(self.results_dir, exist_ok=True)
        
        print(f"训练管理器初始化完成")
        print(f"设备: {device}")
        print(f"结果保存目录: {self.results_dir}")
    
    def train(self, num_episodes: Optional[int] = None, save_interval: int = 50, render_interval: int = 10):
        """训练主循环"""
        if num_episodes is None:
            num_episodes = self.config.num_episodes
        
        print(f"\n开始训练，共 {num_episodes} 个episodes")
        print("=" * 60)
        
        start_time = time.time()
        
        for episode in range(num_episodes):
            episode_start_time = time.time()
            
            # 运行一个episode
            episode_result = self.env.run_episode(
                self.maddpg, 
                max_steps=self.config.tasks_per_episode,
                train=True
            )
            
            # 记录训练数据
            self._record_episode_data(episode, episode_result)
            
            # 打印进度
            if (episode + 1) % render_interval == 0:
                self._print_progress(episode, episode_result, episode_start_time)
            
            # 保存模型
            if (episode + 1) % save_interval == 0:
                self._save_checkpoint(episode + 1)
        
        total_time = time.time() - start_time
        print(f"\n训练完成！总用时: {total_time:.2f}秒")
        
        # 保存最终结果
        self._save_final_results()
        
        # 生成训练报告
        self._generate_training_report()
    
    def _record_episode_data(self, episode: int, episode_result: Dict):
        """记录episode数据"""
        stats = episode_result['episode_stats']
        
        # 记录奖励
        self.training_history['episode_rewards'].append(stats['system_total_reward'])
        
        # 记录系统指标
        self.training_history['system_metrics'].append({
            'episode': episode,
            'average_energy': stats['average_energy'],
            'average_delay': stats['average_delay'],
            'system_mean_reward': stats['system_mean_reward'],
            'system_objective': stats['system_objective']
        })
        
        # 记录约束违反
        self.training_history['constraint_violations'].append(stats['total_constraint_violations'])
        
        # 记录电池电量
        self.training_history['battery_levels'].append(stats['final_battery_levels'])
        
        # 记录网络损失（如果有更新）
        algorithm_stats = self.maddpg.get_training_statistics()
        if algorithm_stats['training_stats']['actor_losses']:
            self.training_history['actor_losses'].append(algorithm_stats['training_stats']['actor_losses'][-1])
        if algorithm_stats['training_stats']['critic_losses']:
            self.training_history['critic_losses'].append(algorithm_stats['training_stats']['critic_losses'][-1])
        if algorithm_stats['training_stats']['noise_levels']:
            self.training_history['noise_levels'].append(algorithm_stats['training_stats']['noise_levels'][-1])
    
    def _print_progress(self, episode: int, episode_result: Dict, episode_start_time: float):
        """打印训练进度"""
        stats = episode_result['episode_stats']
        episode_time = time.time() - episode_start_time
        
        print(f"\nEpisode {episode + 1}/{self.config.num_episodes}:")
        print(f"  时间: {episode_time:.2f}s")
        print(f"  系统总奖励: {stats['system_total_reward']:.4f}")
        print(f"  平均奖励: {stats['system_mean_reward']:.4f}")
        print(f"  平均能耗: {stats['average_energy']:.4f}J")
        print(f"  平均延迟: {stats['average_delay']:.6f}s ({stats['average_delay']*1000:.2f}ms)")
        print(f"  约束违反: {stats['total_constraint_violations']}")
        print(f"  电池耗尽设备: {stats['battery_depletion_count']}/{self.config.M}")
        
        # 打印网络统计
        algorithm_stats = self.maddpg.get_training_statistics()
        print(f"  缓冲区: {algorithm_stats['buffer_size']}/{algorithm_stats['buffer_capacity']}")
        print(f"  噪声水平: {algorithm_stats['current_noise_level']:.4f}")
        
        if algorithm_stats['average_actor_loss'] > 0:
            print(f"  Actor损失: {algorithm_stats['average_actor_loss']:.6f}")
            print(f"  Critic损失: {algorithm_stats['average_critic_loss']:.6f}")
    
    def _save_checkpoint(self, episode: int):
        """保存检查点"""
        checkpoint_dir = os.path.join(self.results_dir, f"checkpoint_ep{episode}")
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # 保存模型
        self.maddpg.save_models(checkpoint_dir, episode)
        
        # 保存训练历史
        history_path = os.path.join(checkpoint_dir, "training_history.json")
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2, ensure_ascii=False)
        
        print(f"  检查点已保存: {checkpoint_dir}")
    
    def _save_final_results(self):
        """保存最终结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存最终模型
        final_model_dir = os.path.join(self.results_dir, f"final_models_{timestamp}")
        os.makedirs(final_model_dir, exist_ok=True)
        self.maddpg.save_models(final_model_dir, self.config.num_episodes)
        
        # 保存完整训练历史
        history_path = os.path.join(self.results_dir, f"final_training_history_{timestamp}.json")
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2, ensure_ascii=False)
        
        # 保存配置
        config_path = os.path.join(self.results_dir, f"config_{timestamp}.json")
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config.__dict__, f, indent=2, ensure_ascii=False)
        
        print(f"最终结果已保存: {self.results_dir}")
    
    def _generate_training_report(self):
        """生成训练报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('MADDPG训练结果报告', fontsize=16)
        
        # 奖励曲线
        axes[0, 0].plot(self.training_history['episode_rewards'])
        axes[0, 0].set_title('Episode总奖励变化')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('总奖励')
        axes[0, 0].grid(True)
        
        # 能耗和延迟
        episodes = [m['episode'] for m in self.training_history['system_metrics']]
        energies = [m['average_energy'] for m in self.training_history['system_metrics']]
        delays = [m['average_delay'] * 1000 for m in self.training_history['system_metrics']]  # 转换为ms
        
        axes[0, 1].plot(episodes, energies, 'b-', label='平均能耗 (J)')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('平均能耗 (J)', color='b')
        axes[0, 1].tick_params(axis='y', labelcolor='b')
        
        ax2 = axes[0, 1].twinx()
        ax2.plot(episodes, delays, 'r-', label='平均延迟 (ms)')
        ax2.set_ylabel('平均延迟 (ms)', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        axes[0, 1].set_title('能耗与延迟变化')
        axes[0, 1].grid(True)
        
        # 约束违反
        axes[0, 2].plot(self.training_history['constraint_violations'])
        axes[0, 2].set_title('约束违反次数')
        axes[0, 2].set_xlabel('Episode')
        axes[0, 2].set_ylabel('违反次数')
        axes[0, 2].grid(True)
        
        # 电池电量分布
        if self.training_history['battery_levels']:
            final_batteries = self.training_history['battery_levels'][-1]
            axes[1, 0].bar(range(len(final_batteries)), final_batteries)
            axes[1, 0].set_title('最终电池电量分布')
            axes[1, 0].set_xlabel('设备ID')
            axes[1, 0].set_ylabel('电量比例')
            axes[1, 0].set_ylim(0, 1)
            axes[1, 0].grid(True)
        
        # 网络损失
        if self.training_history['actor_losses'] and self.training_history['critic_losses']:
            axes[1, 1].plot(self.training_history['actor_losses'], label='Actor Loss')
            axes[1, 1].plot(self.training_history['critic_losses'], label='Critic Loss')
            axes[1, 1].set_title('网络训练损失')
            axes[1, 1].set_xlabel('更新次数')
            axes[1, 1].set_ylabel('损失值')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        # 噪声水平
        if self.training_history['noise_levels']:
            axes[1, 2].plot(self.training_history['noise_levels'])
            axes[1, 2].set_title('探索噪声衰减')
            axes[1, 2].set_xlabel('更新次数')
            axes[1, 2].set_ylabel('噪声标准差')
            axes[1, 2].grid(True)
        
        plt.tight_layout()
        
        # 保存图表
        plot_path = os.path.join(self.results_dir, f"training_progress_{timestamp}.png")
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"训练报告已生成: {plot_path}")
    
    def evaluate(self, num_episodes: int = 10, load_model_path: Optional[str] = None) -> Dict:
        """评估模型性能"""
        if load_model_path:
            # 加载指定模型
            episode_num = self.config.num_episodes  # 默认加载最终模型
            self.maddpg.load_models(load_model_path, episode_num)
        
        # 设置为评估模式
        self.maddpg.set_eval_mode()
        
        print(f"\n开始评估，共 {num_episodes} 个episodes")
        
        eval_results = []
        
        for episode in range(num_episodes):
            # 运行episode（不训练）
            episode_result = self.env.run_episode(
                self.maddpg,
                max_steps=self.config.tasks_per_episode,
                train=False
            )
            
            eval_results.append(episode_result['episode_stats'])
            
            if (episode + 1) % 5 == 0:
                stats = episode_result['episode_stats']
                print(f"评估Episode {episode + 1}: 奖励={stats['system_total_reward']:.4f}, "
                      f"能耗={stats['average_energy']:.4f}J, "
                      f"延迟={stats['average_delay']*1000:.2f}ms")
        
        # 计算评估统计
        eval_stats = self._compute_evaluation_statistics(eval_results)
        
        # 恢复训练模式
        self.maddpg.set_train_mode()
        
        return eval_stats
    
    def _compute_evaluation_statistics(self, eval_results: List[Dict]) -> Dict:
        """计算评估统计"""
        rewards = [result['system_total_reward'] for result in eval_results]
        energies = [result['average_energy'] for result in eval_results]
        delays = [result['average_delay'] for result in eval_results]
        violations = [result['total_constraint_violations'] for result in eval_results]
        
        return {
            'mean_reward': np.mean(rewards),
            'std_reward': np.std(rewards),
            'mean_energy': np.mean(energies),
            'std_energy': np.std(energies),
            'mean_delay': np.mean(delays),
            'std_delay': np.std(delays),
            'mean_violations': np.mean(violations),
            'success_rate': np.mean([v == 0 for v in violations]),
            'detailed_results': eval_results
        }

def main():
    """主函数"""
    print("=" * 60)
    print("MADDPG边缘推理优化系统")
    print("=" * 60)
    
    # 创建配置
    config = Config()
    config.validate_config()
    config.print_config_summary()
    
    # 检查CUDA可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建训练管理器
    trainer = TrainingManager(config, device)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    trainer.env.seed(42)
    
    try:
        # 开始训练
        print("\n开始训练...")
        trainer.train(
            num_episodes=config.num_episodes,
            save_interval=50,
            render_interval=10
        )
        
        # 评估最终模型
        print("\n开始最终评估...")
        eval_stats = trainer.evaluate(num_episodes=20)
        
        print("\n最终评估结果:")
        print(f"平均奖励: {eval_stats['mean_reward']:.4f} ± {eval_stats['std_reward']:.4f}")
        print(f"平均能耗: {eval_stats['mean_energy']:.4f} ± {eval_stats['std_energy']:.4f} J")
        print(f"平均延迟: {eval_stats['mean_delay']*1000:.2f} ± {eval_stats['std_delay']*1000:.2f} ms")
        print(f"约束满足率: {eval_stats['success_rate']:.2%}")
        
    except KeyboardInterrupt:
        print("\n训练被用户中断")
        # 保存当前进度
        trainer._save_checkpoint(trainer.env.current_episode)
        print("当前进度已保存")
    
    except Exception as e:
        print(f"\n训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n程序结束")

if __name__ == "__main__":
    main()
