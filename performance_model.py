"""
性能评估模型
包含能耗模型、延迟模型、自适应权重机制、性能评估器和奖励函数设计
"""

import numpy as np
from typing import List, Dict, Tuple
import math

class EnergyModel:
    """能耗模型类"""
    
    def __init__(self, config):
        self.config = config
    
    def compute_computation_energy(self, frequency: float, processing_time: float) -> float:
        """计算计算能耗
        公式: E_comp = κ * f³ * T_local (f单位GHz, T单位秒)
        """
        if processing_time <= 0:
            return 0
        frequency_ghz = frequency / 1e9
        return self.config.kappa * (frequency_ghz ** 3) * processing_time
    
    def compute_communication_energy(self, P_tx: float, data_size: float, transmission_rate: float) -> float:
        """计算通信能耗
        公式: E_comm = P_tx * T_comm = P_tx * (D_tx / R)
        """
        if data_size <= 0:
            return 0
        transmission_time = data_size / transmission_rate
        return P_tx * transmission_time
    
    def compute_static_energy(self, P_static: float, total_time: float) -> float:
        """计算静态能耗
        公式: E_idle = P_static * T_total
        """
        if total_time <= 0:
            return 0
        return P_static * total_time
    
    def compute_total_energy(self, computation_energy: float, communication_energy: float, 
                           static_energy: float) -> Dict:
        """计算总能耗"""
        total = computation_energy + communication_energy + static_energy
        
        return {
            'computation': computation_energy,
            'communication': communication_energy,
            'static': static_energy,
            'total': total
        }

class DelayModel:
    """延迟模型类"""
    
    def __init__(self, config):
        self.config = config
    
    def compute_local_processing_time(self, flops: float, frequency: float, g_device: float) -> float:
        """计算本地处理延迟
        公式: T_local = C_local / (f_m * g_m)
        """
        if flops <= 0:
            return 0
        return flops / (frequency * g_device)
    
    def compute_communication_time(self, data_size: float, transmission_rate: float) -> float:
        """计算通信延迟
        公式: T_comm = D_tx / R_tx
        """
        if data_size <= 0:
            return 0
        return data_size / transmission_rate
    
    def compute_edge_processing_time(self, flops: float, server_frequency: float, g_server: float) -> float:
        """计算边缘处理延迟
        公式: T_edge = C_edge / (f_server * g_server)
        """
        if flops <= 0:
            return 0
        return flops / (server_frequency * g_server)
    
    def compute_total_delay(self, local_time: float, communication_time: float, 
                          edge_time: float, execution_mode: str) -> Dict:
        """计算总延迟
        根据执行模式计算总延迟:
        - local_only: T_total = T_local
        - edge_only: T_total = T_comm + T_edge  
        - split_execution: T_total = T_local + T_comm + T_edge
        """
        if execution_mode == 'local_only':
            total = local_time
        elif execution_mode == 'edge_only':
            total = communication_time + edge_time
        elif execution_mode == 'split_execution':
            total = local_time + communication_time + edge_time
        else:
            total = local_time + communication_time + edge_time  # 默认分割执行
        
        return {
            'local': local_time,
            'communication': communication_time,
            'edge': edge_time,
            'total': total,
            'execution_mode': execution_mode
        }

class AdaptiveWeightMechanism:
    """自适应权重机制类"""
    
    def __init__(self, config):
        self.config = config
        self.beta = config.beta
        self.theta = config.theta
    
    def calculate_weight(self, current_battery: float, max_battery: float) -> float:
        """计算自适应权重
        公式: α_m = 1 / (1 + exp(-β * (B_m/B_max - θ)))
        """
        battery_ratio = current_battery / max_battery
        exponent = -self.beta * (battery_ratio - self.theta)
        # 防止数值溢出
        exponent = np.clip(exponent, -500, 500)
        alpha = 1.0 / (1.0 + np.exp(exponent))
        return alpha
    
    def get_weight_interpretation(self, alpha: float) -> str:
        """获取权重语义解释"""
        if alpha > 0.7:
            return "优先延迟最小化 (电量充足)"
        elif alpha > 0.3:
            return "平衡优化 (电量适中)"
        else:
            return "优先能耗最小化 (电量不足)"
    
    def calculate_batch_weights(self, battery_levels: List[float], max_batteries: List[float]) -> List[float]:
        """批量计算自适应权重"""
        weights = []
        for current, max_bat in zip(battery_levels, max_batteries):
            weight = self.calculate_weight(current, max_bat)
            weights.append(weight)
        return weights

class PerformanceEvaluator:
    """性能评估器"""
    
    def __init__(self, config):
        self.config = config
        self.energy_model = EnergyModel(config)
        self.delay_model = DelayModel(config)
        self.adaptive_weight = AdaptiveWeightMechanism(config)
    
    def evaluate_single_device_performance(self, device, task_info: Dict, server=None) -> Dict:
        """评估单个设备的性能"""
        workload = task_info['workload_distribution']
        
        # 计算延迟
        local_time = self.delay_model.compute_local_processing_time(
            workload['local_flops'], device.current_frequency, device.g_device
        )
        
        communication_time = self.delay_model.compute_communication_time(
            workload['communication_data'], device.R_tx
        )
        
        edge_time = 0
        if server and workload['edge_flops'] > 0:
            edge_time = self.delay_model.compute_edge_processing_time(
                workload['edge_flops'], server.f_server, server.g_server
            )
        
        delay_info = self.delay_model.compute_total_delay(
            local_time, communication_time, edge_time, workload['execution_mode']
        )
        
        # 计算能耗
        computation_energy = self.energy_model.compute_computation_energy(
            device.current_frequency, local_time
        )
        
        communication_energy = self.energy_model.compute_communication_energy(
            device.P_tx, workload['communication_data'], device.R_tx
        )
        
        static_energy = self.energy_model.compute_static_energy(
            device.P_static, delay_info['total']
        )
        
        energy_info = self.energy_model.compute_total_energy(
            computation_energy, communication_energy, static_energy
        )
        
        # 计算自适应权重
        adaptive_weight = device.get_adaptive_weight()
        
        # 计算个体目标函数
        individual_objective = adaptive_weight * delay_info['total'] + (1 - adaptive_weight) * energy_info['total']
        
        return {
            'device_id': device.device_id,
            'delay_info': delay_info,
            'energy_info': energy_info,
            'adaptive_weight': adaptive_weight,
            'individual_objective': individual_objective,
            'task_info': task_info,
            'battery_ratio': device.current_battery / device.max_battery  # 添加电池比例信息
        }
    
    def evaluate_system_performance(self, devices: List, servers: List, tasks_info: List[Dict]) -> Dict:
        """评估系统整体性能"""
        device_performances = []
        constraint_violations = []
        
        for i, (device, task_info) in enumerate(zip(devices, tasks_info)):
            # 获取对应的服务器
            server = None
            if task_info.get('target_server') is not None:
                server_id = task_info['target_server']
                if 0 <= server_id < len(servers):
                    server = servers[server_id]
            
            # 评估设备性能
            device_perf = self.evaluate_single_device_performance(device, task_info, server)
            device_performances.append(device_perf)
            
            # 检查约束违反
            device_violations = self._check_constraints(device, device_perf['delay_info'], task_info)
            constraint_violations.extend(device_violations)
        
        # 计算系统级指标
        system_metrics = self._compute_system_metrics(device_performances)
        
        return {
            'device_performances': device_performances,
            'constraint_violations': constraint_violations,
            'system_metrics': system_metrics
        }
    
    def _check_constraints(self, device, delay_info: Dict, task_info: Dict) -> List[Dict]:
        """检查约束违反情况"""
        violations = []
        
        # 电池约束
        if device.is_depleted():
            violations.append({
                'type': 'battery_depletion',
                'device_id': device.device_id,
                'current_battery': device.current_battery,
                'min_battery': device.min_battery
            })
        
        # 延迟约束
        if delay_info['total'] > self.config.T_max:
            violations.append({
                'type': 'delay_violation',
                'device_id': device.device_id,
                'actual_delay': delay_info['total'],
                'max_delay': self.config.T_max
            })
        
        # 频率约束
        if not device.is_frequency_valid():
            violations.append({
                'type': 'frequency_violation',
                'device_id': device.device_id,
                'current_frequency': device.current_frequency,
                'valid_range': [device.f_min, device.f_max]
            })
        
        # 精度约束
        expected_accuracy = task_info.get('expected_accuracy', 1.0)
        required_accuracy = task_info.get('accuracy_requirement', 0.0)
        if expected_accuracy < required_accuracy:
            violations.append({
                'type': 'accuracy_violation',
                'device_id': device.device_id,
                'expected_accuracy': expected_accuracy,
                'required_accuracy': required_accuracy
            })
        
        return violations
    
    def _compute_system_metrics(self, device_performances: List[Dict]) -> Dict:
        """计算系统级指标"""
        delays = [perf['delay_info']['total'] for perf in device_performances]
        energies = [perf['energy_info']['total'] for perf in device_performances]
        objectives = [perf['individual_objective'] for perf in device_performances]
        weights = [perf['adaptive_weight'] for perf in device_performances]
        
        return {
            'average_delay': np.mean(delays),
            'average_energy': np.mean(energies),
            'system_objective': np.mean(objectives),
            'average_adaptive_weight': np.mean(weights),
            'delay_std': np.std(delays),
            'energy_std': np.std(energies),
            'max_delay': np.max(delays),
            'max_energy': np.max(energies)
        }
    
    def compute_reward(self, device_performances: List[Dict], constraint_violations: List[Dict],
                      episode_num: int = 0) -> List[float]:
        """计算奖励函数
        基于性能分数和约束惩罚设计奖励函数
        添加渐进式学习奖励
        """
        rewards = []
        
        # 按设备分组约束违反
        violations_by_device = {}
        for violation in constraint_violations:
            device_id = violation.get('device_id', -1)
            if device_id not in violations_by_device:
                violations_by_device[device_id] = []
            violations_by_device[device_id].append(violation)
        
        for perf in device_performances:
            device_id = perf['device_id']
            
            # 获取原始性能指标
            delay = perf['delay_info']['total']  # 秒
            energy = perf['energy_info']['total']  # 焦耳
            alpha = perf['adaptive_weight']
            
            # 归一化性能指标
            delay_ms = delay * 1000
            normalized_delay = min(delay_ms / 150.0, 2.0)  # 150ms为参考
            normalized_energy = min(energy / 50.0, 2.0)    # 50J为参考
            
            # 基础性能奖励 (0-100分制)
            delay_score = max(0, 100 - normalized_delay * 50)
            energy_score = max(0, 100 - normalized_energy * 50)

            # 电池管理奖励
            battery_ratio = perf.get('battery_ratio', 1.0)  # 从性能数据中获取电池比例
            if battery_ratio > 0.8:
                battery_bonus = 10  # 电量充足时给予奖励
            elif battery_ratio > 0.5:
                battery_bonus = 5   # 电量适中时给予小奖励
            elif battery_ratio > 0.2:
                battery_bonus = 0   # 电量偏低时无奖励
            else:
                battery_bonus = -20 # 电量过低时惩罚

            # 加权组合基础奖励
            base_reward = alpha * delay_score + (1 - alpha) * energy_score + battery_bonus
            
            # 约束惩罚
            penalty = 0
            device_violations = violations_by_device.get(device_id, [])
            for violation in device_violations:
                if violation['type'] == 'battery_depletion':
                    penalty += self.config.penalty_battery_depletion
                elif violation['type'] == 'delay_violation':
                    penalty += self.config.penalty_delay_violation
                elif violation['type'] == 'accuracy_violation':
                    penalty += self.config.penalty_accuracy_violation
                elif violation['type'] == 'frequency_violation':
                    penalty += self.config.penalty_frequency_violation
                elif violation['type'] == 'server_overload':
                    penalty += self.config.penalty_server_overload
            
            # 渐进式学习奖励（鼓励长期改进）
            learning_bonus = min(episode_num * 0.1, 10)  # 随episode增加的奖励，最多10分

            # 最终奖励
            individual_reward = base_reward - penalty + learning_bonus
            individual_reward = max(individual_reward, -200)  # 最低-200分
            individual_reward = min(individual_reward, 120)   # 最高120分（增加上限以容纳学习奖励）

            rewards.append(individual_reward)
        
        return rewards
