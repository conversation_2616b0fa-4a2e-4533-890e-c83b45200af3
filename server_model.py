"""
边缘服务器模型
包含服务器状态管理、任务处理、负载管理、容量约束检查等功能
"""

import numpy as np
from typing import List, Dict, Optional
import time

class EdgeServer:
    """边缘服务器类"""
    
    def __init__(self, server_id: int, config):
        self.server_id = server_id
        self.config = config
        
        # 服务器硬件参数（随机初始化，模拟异构性）
        self.f_server = np.random.uniform(*config.f_server_range)  # 服务器频率
        self.g_server = config.g_server  # 每时钟周期FLOPs
        self.max_load = config.max_load  # 最大负载率
        
        # 容量计算
        self.max_capacity_per_slot = self.f_server * self.g_server * config.delta_t * self.max_load
        
        # 当前状态
        self.current_load = 0.0  # 当前负载率 [0, 1]
        self.active_tasks = []   # 当前活跃任务列表
        
        # 统计信息
        self.processed_tasks = 0
        self.total_flops_processed = 0
        self.load_history = []
        self.utilization_history = []
    
    def get_state(self) -> np.ndarray:
        """获取服务器状态向量 [current_load, f_server_ghz, active_tasks_count, max_capacity_gflops]"""
        f_server_ghz = self.f_server / 1e9
        active_tasks_count = len(self.active_tasks)
        max_capacity_gflops = self.max_capacity_per_slot / 1e9
        
        return np.array([self.current_load, f_server_ghz, active_tasks_count, max_capacity_gflops])
    
    def can_accept_task(self, flops_required: float) -> bool:
        """检查是否可以接受新任务
        判断当前容量使用量 + 新任务需求 <= 最大容量
        """
        current_capacity_used = sum([task['flops'] for task in self.active_tasks])
        return (current_capacity_used + flops_required) <= self.max_capacity_per_slot
    
    def add_task(self, task_info: Dict) -> bool:
        """添加任务到服务器
        task_info包含: task_id, device_id, flops, arrival_time等
        """
        if self.can_accept_task(task_info['flops']):
            task_info['arrival_time'] = time.time()
            self.active_tasks.append(task_info)
            self.update_load()
            return True
        return False
    
    def compute_processing_time(self, flops: float) -> float:
        """计算边缘处理时间
        公式: T_edge = C_edge / (f_server * g_server)
        """
        if flops <= 0:
            return 0
        return flops / (self.f_server * self.g_server)
    
    def process_tasks(self) -> List[Dict]:
        """处理所有活跃任务，返回完成的任务信息"""
        completed_tasks = []
        
        for task in self.active_tasks[:]:  # 使用切片复制避免修改迭代中的列表
            # 计算处理时间
            processing_time = self.compute_processing_time(task['flops'])
            
            # 模拟任务完成（简化处理，实际中可能需要更复杂的调度）
            task['processing_time'] = processing_time
            task['completion_time'] = time.time()
            
            # 更新统计信息
            self.processed_tasks += 1
            self.total_flops_processed += task['flops']
            
            completed_tasks.append(task)
            self.active_tasks.remove(task)
        
        # 更新负载
        self.update_load()
        
        return completed_tasks
    
    def update_load(self):
        """更新当前负载率"""
        if len(self.active_tasks) == 0:
            self.current_load = 0.0
        else:
            total_flops = sum([task['flops'] for task in self.active_tasks])
            self.current_load = min(1.0, total_flops / self.max_capacity_per_slot)
        
        self.load_history.append(self.current_load)
        
        # 计算利用率（考虑时间因素）
        utilization = self.current_load * self.max_load
        self.utilization_history.append(utilization)
    
    def get_capacity_info(self) -> Dict:
        """获取容量信息"""
        current_capacity_used = sum([task['flops'] for task in self.active_tasks])
        available_capacity = self.max_capacity_per_slot - current_capacity_used
        
        return {
            'max_capacity': self.max_capacity_per_slot,
            'current_used': current_capacity_used,
            'available': available_capacity,
            'utilization_ratio': current_capacity_used / self.max_capacity_per_slot,
            'load_ratio': self.current_load
        }
    
    def is_overloaded(self) -> bool:
        """检查服务器是否过载"""
        return self.current_load > 1.0
    
    def reset(self):
        """重置服务器状态"""
        self.current_load = 0.0
        self.active_tasks.clear()
        self.processed_tasks = 0
        self.total_flops_processed = 0
        self.load_history.clear()
        self.utilization_history.clear()
    
    def get_server_info(self) -> Dict:
        """获取服务器完整信息"""
        capacity_info = self.get_capacity_info()
        
        return {
            'server_id': self.server_id,
            'f_server_ghz': self.f_server / 1e9,
            'max_capacity_gflops': self.max_capacity_per_slot / 1e9,
            'current_load': self.current_load,
            'active_tasks': len(self.active_tasks),
            'processed_tasks': self.processed_tasks,
            'total_flops_processed_gflops': self.total_flops_processed / 1e9,
            'is_overloaded': self.is_overloaded(),
            'capacity_info': capacity_info
        }
    
    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        avg_load = np.mean(self.load_history) if self.load_history else 0.0
        avg_utilization = np.mean(self.utilization_history) if self.utilization_history else 0.0
        
        return {
            'average_load': avg_load,
            'average_utilization': avg_utilization,
            'peak_load': max(self.load_history) if self.load_history else 0.0,
            'load_variance': np.var(self.load_history) if self.load_history else 0.0,
            'throughput_gflops': self.total_flops_processed / 1e9,
            'task_completion_rate': self.processed_tasks
        }

class ServerManager:
    """服务器管理器"""
    
    def __init__(self, config):
        self.config = config
        self.servers = []
        self._initialize_servers()
    
    def _initialize_servers(self):
        """初始化所有服务器"""
        for i in range(self.config.N):
            server = EdgeServer(i, self.config)
            self.servers.append(server)
    
    def get_all_states(self) -> np.ndarray:
        """获取所有服务器状态矩阵 [N, 4]"""
        states = []
        for server in self.servers:
            states.append(server.get_state())
        return np.array(states)
    
    def get_server(self, server_id: int) -> EdgeServer:
        """获取指定服务器"""
        if 0 <= server_id < len(self.servers):
            return self.servers[server_id]
        return None
    
    def allocate_task(self, task_info: Dict, preferred_server_id: int = None) -> Optional[int]:
        """分配任务到服务器
        返回分配到的服务器ID，如果分配失败返回None
        """
        # 如果指定了首选服务器，先尝试分配到该服务器
        if preferred_server_id is not None:
            server = self.get_server(preferred_server_id)
            if server and server.add_task(task_info):
                return preferred_server_id
        
        # 如果首选服务器不可用，尝试其他服务器（负载均衡）
        # 按负载从低到高排序
        server_loads = [(i, server.current_load) for i, server in enumerate(self.servers)]
        server_loads.sort(key=lambda x: x[1])
        
        for server_id, _ in server_loads:
            server = self.servers[server_id]
            if server.add_task(task_info):
                return server_id
        
        return None  # 所有服务器都无法接受任务
    
    def allocate_tasks(self, task_allocations: List[Dict]) -> List[Dict]:
        """批量分配任务"""
        allocation_results = []
        
        for allocation in task_allocations:
            task_info = {
                'task_id': allocation['task_id'],
                'device_id': allocation['device_id'],
                'flops': allocation['task'].compute_workload_distribution()['edge_flops']
            }
            
            allocated_server_id = self.allocate_task(task_info, allocation['server_id'])
            
            result = {
                'task_id': allocation['task_id'],
                'device_id': allocation['device_id'],
                'requested_server_id': allocation['server_id'],
                'allocated_server_id': allocated_server_id,
                'allocation_success': allocated_server_id is not None,
                'task_info': task_info
            }
            
            allocation_results.append(result)
        
        return allocation_results
    
    def process_all_tasks(self) -> Dict:
        """处理所有服务器的任务"""
        all_completed_tasks = []
        server_results = {}
        
        for server in self.servers:
            completed_tasks = server.process_tasks()
            all_completed_tasks.extend(completed_tasks)
            
            server_results[server.server_id] = {
                'completed_tasks': len(completed_tasks),
                'current_load': server.current_load,
                'active_tasks': len(server.active_tasks)
            }
        
        return {
            'completed_tasks': all_completed_tasks,
            'server_results': server_results,
            'total_completed': len(all_completed_tasks)
        }
    
    def check_capacity_constraints(self) -> List[Dict]:
        """检查容量约束违反情况"""
        violations = []
        
        for server in self.servers:
            if server.is_overloaded():
                violations.append({
                    'type': 'server_overload',
                    'server_id': server.server_id,
                    'current_load': server.current_load,
                    'max_load': 1.0,
                    'capacity_info': server.get_capacity_info()
                })
        
        return violations
    
    def reset_all(self):
        """重置所有服务器"""
        for server in self.servers:
            server.reset()
    
    def get_system_statistics(self) -> Dict:
        """获取系统级统计信息"""
        total_processed = sum(server.processed_tasks for server in self.servers)
        total_flops = sum(server.total_flops_processed for server in self.servers)
        avg_load = np.mean([server.current_load for server in self.servers])
        overloaded_count = sum(1 for server in self.servers if server.is_overloaded())
        
        # 计算总容量和使用情况
        total_capacity = sum(server.max_capacity_per_slot for server in self.servers)
        total_used = sum(sum(task['flops'] for task in server.active_tasks) for server in self.servers)
        
        return {
            'total_processed_tasks': total_processed,
            'total_flops_processed_gflops': total_flops / 1e9,
            'average_load': avg_load,
            'overloaded_server_count': overloaded_count,
            'total_servers': len(self.servers),
            'total_capacity_gflops': total_capacity / 1e9,
            'total_capacity_used_gflops': total_used / 1e9,
            'system_utilization': total_used / total_capacity if total_capacity > 0 else 0.0,
            'constraint_violations': len(self.check_capacity_constraints())
        }
    
    def get_all_server_info(self) -> List[Dict]:
        """获取所有服务器的详细信息"""
        return [server.get_server_info() for server in self.servers]
    
    def print_server_status(self):
        """打印服务器状态摘要"""
        print("\n" + "="*60)
        print("服务器状态摘要")
        print("="*60)
        
        for server in self.servers:
            info = server.get_server_info()
            print(f"服务器 {info['server_id']}: "
                  f"负载 {info['current_load']:.2%}, "
                  f"频率 {info['f_server_ghz']:.2f}GHz, "
                  f"活跃任务 {info['active_tasks']}")
        
        stats = self.get_system_statistics()
        print(f"\n系统统计: 平均负载 {stats['average_load']:.2%}, "
              f"过载服务器 {stats['overloaded_server_count']}/{stats['total_servers']}")
        print("="*60)
