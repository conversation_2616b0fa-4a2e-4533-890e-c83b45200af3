# 异构多边缘服务器协作深度神经网络推理优化框架详细文档

## 1. 框架概述

本框架是一个基于多智能体深度强化学习（MADDPG）的异构多边缘服务器协作深度神经网络推理优化系统。该框架旨在优化移动设备在边缘计算环境中的深度学习推理任务卸载策略，平衡能耗、延迟和准确率，同时考虑设备电量约束和服务器异构性。

### 1.1 核心特性
- **多智能体协作**：基于MADDPG算法的去中心化执行、中心化训练
- **异构环境支持**：支持不同性能的移动设备和边缘服务器
- **自适应权重机制**：根据设备电量动态调整能耗与延迟的优化权重
- **早退出机制**：支持ResNet-50的多层早退出点以平衡精度和计算量
- **实时约束处理**：考虑延迟约束、电量约束、精度约束等多重约束

### 1.2 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   移动设备群    │    │   边缘服务器群   │    │   MADDPG算法    │
│  (M=4个设备)    │◄──►│  (N=4个服务器)   │◄──►│  (4个智能体)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   环境管理器    │
                    │ (任务调度与性能  │
                    │    评估)       │
                    └─────────────────┘
```

## 2. 配置参数详解 (config.py)

### 2.1 系统基础参数
```python
self.M = 4                    # 设备数量
self.N = 4                    # 边缘服务器数量  
self.delta_t = 0.15          # 全局时隙 150ms
```

### 2.2 设备参数
#### 2.2.1 电池参数
```python
self.max_battery_range = [40, 60]  # 最大电池容量范围 (mAh)
self.B_min_ratio = 0.05              # 最小电池电量比例 (5%)
self.voltage = 5.0                   # 工作电压 (V)
```

#### 2.2.2 计算参数
```python
self.f_min = 0.3e9           # 最小CPU频率 (0.3 GHz)
self.f_max = 1.2e9           # 最大CPU频率 (1.2 GHz)
self.g_device = 153          # 设备每时钟周期浮点运算数 (FLOPs/cycle)
```

#### 2.2.3 能耗参数
```python
self.kappa = 1.3                    # 能耗系数 (W/GHz³)
self.P_static_range = [3, 8]        # 静态功耗范围 (W)
```

**能耗计算公式：**
- 计算能耗：$E_{comp} = \kappa \cdot f^3 \cdot T_{local}$ (f单位GHz, T单位ms)
- 通信能耗：$E_{comm} = P_{tx} \cdot T_{comm}$
- 静态能耗：$E_{idle} = P_{static} \cdot T_{total}$
- 总能耗：$E_{total} = E_{comp} + E_{comm} + E_{idle}$

#### 2.2.4 通信参数
```python
self.P_tx_range = [0.093, 0.115]    # 传输功率范围 (W)
self.R_range = [45e6, 65e6]        # 传输速率范围 (bps)
```

**通信时间公式：**
$T_{comm} = \frac{D_{tx}}{R_{tx}}$

### 2.3 服务器参数
```python
self.f_server_range = [2.2e9, 2.8e9]  # 服务器频率范围 (Hz)
self.g_server = 380                   # 服务器每时钟周期浮点运算数
self.max_load = 0.75                  # 最大负载率 (75%)
```

**边缘处理时间公式：**
$T_{edge} = \frac{C_{edge}}{f_{server} \cdot g_{server}}$

### 2.4 任务参数
```python
self.dataset = "cifar-10"             # 数据集
self.model_name = "resnet50"          # 模型名称
self.num_classes = 10                 # 分类数量
self.acc_min_range = [0.70, 0.91]     # 精度要求范围
self.T_max = 0.150                    # 最大延迟约束 (150ms)
```

### 2.5 早退出点配置
```python
self.exit_points = {
    "Full": {"layer_id": 164, "accuracy": 0.9133, "cumulative_flops": 1942995968.0},
    "Exit_1": {"layer_id": 32, "accuracy": 0.77, "cumulative_flops": 221380608.0},
    "Exit_2": {"layer_id": 73, "accuracy": 0.8731, "cumulative_flops": 771855360.0},
    "Exit_3": {"layer_id": 105, "accuracy": 0.9038, "cumulative_flops": 1250425856.0},
    "Exit_4": {"layer_id": 135, "accuracy": 0.9113, "cumulative_flops": 1464640512.0},
    "Exit_5": {"layer_id": 150, "accuracy": 0.9121, "cumulative_flops": 1817076736.0}
}
```

### 2.6 自适应权重参数
```python
self.beta = 10.0    # 敏感度参数
self.theta = 0.6    # 阈值参数 (60%电量时开始优先能耗)
```

**自适应权重公式：**
$\alpha_m = \frac{1}{1 + \exp(-\beta \cdot (\frac{B_m}{B_{max}} - \theta))}$

其中：
- $B_m$：当前电池电量
- $B_{max}$：最大电池容量
- $\alpha_m > 0.7$：优先延迟最小化（电量充足）
- $0.3 < \alpha_m < 0.7$：平衡优化（电量适中）
- $\alpha_m < 0.3$：优先能耗最小化（电量不足）

### 2.7 MADDPG算法参数
```python
self.lr_actor = 1e-5              # Actor学习率
self.lr_critic = 5e-5             # Critic学习率
self.gamma = 0.99                 # 折扣因子
self.tau = 0.005                  # 软更新系数
self.buffer_size = 4000           # 经验回放缓冲区大小
self.batch_size = 32              # 批次大小
self.update_frequency = 2         # 更新频率
self.warmup_steps = 100           # 预热步数

# 噪声调度
self.initial_noise = 0.2          # 初始噪声标准差
self.final_noise = 0.05           # 最终噪声标准差
self.noise_decay_ratio = 0.9      # 噪声衰减比例

# 网络架构
self.hidden_dims = [256, 128, 64] # 隐藏层维度
self.dropout_rate = 0.1           # Dropout率
self.grad_clip = 0.5              # 梯度裁剪阈值
```

### 2.8 训练参数
```python
self.num_episodes = 200           # 训练轮数
self.tasks_per_episode = 150      # 每轮任务数
```

### 2.9 约束惩罚参数
```python
self.penalty_battery_depletion = 100    # 电池耗尽惩罚
self.penalty_delay_violation = 100       # 延迟违反惩罚
self.penalty_accuracy_violation = 200    # 精度违反惩罚
self.penalty_server_overload = 100       # 服务器过载惩罚
self.penalty_frequency_violation = 100   # 频率违反惩罚
```

## 3. 设备模型详解 (device_model.py)

### 3.1 LocalDevice类

#### 3.1.1 状态向量
设备状态向量定义为3维：
```python
state = [battery_ratio, frequency_ratio, absolute_battery]
```
其中：
- `battery_ratio = current_battery / max_battery` ∈ [0, 1]
- `frequency_ratio = (current_frequency - f_min) / (f_max - f_min)` ∈ [0, 1]
- `absolute_battery = current_battery / 1000.0` (转换为Ah)

#### 3.1.2 核心计算方法

**本地处理时间：**
```python
def compute_local_processing_time(self, flops):
    return flops / (self.current_frequency * self.g_device)
```
公式：$T_{local} = \frac{C_{local}}{f_m \cdot g_m}$

**通信传输时间：**
```python
def compute_communication_time(self, data_size):
    return data_size / self.R_tx
```
公式：$T_{comm} = \frac{D_{tx}}{R_{tx}}$

**计算能耗：**
```python
def compute_computation_energy(self, processing_time):
    frequency_ghz = self.current_frequency / 1e9
    return self.kappa * (frequency_ghz ** 3) * processing_time
```
公式：$E_{comp} = \kappa \cdot f^3 \cdot T_{local}$

**电池更新：**
```python
def update_battery(self, energy_consumed):
    # 将焦耳转换为mAh
    energy_mah = energy_consumed * 1000 / (self.config.voltage * 3600)
    self.current_battery = max(0, self.current_battery - energy_mah)
```
转换公式：$mAh = \frac{E(J) \times 1000}{V \times 3600}$

#### 3.1.3 自适应权重计算
```python
def get_adaptive_weight(self):
    battery_ratio = self.current_battery / self.max_battery
    exponent = -self.config.beta * (battery_ratio - self.config.theta)
    alpha = 1.0 / (1.0 + np.exp(exponent))
    return alpha
```

### 3.2 DeviceManager类
负责管理所有设备，提供批量操作接口：
- `get_all_states()`: 获取所有设备状态矩阵 [M, 3]
- `update_all_batteries()`: 批量更新所有设备电池
- `check_constraints()`: 检查所有设备约束违反情况

## 4. 服务器模型详解 (server_model.py)

### 4.1 EdgeServer类

#### 4.1.1 状态向量
服务器状态向量定义为4维：
```python
state = [current_load, f_server_ghz, active_tasks_count, max_capacity_gflops]
```

#### 4.1.2 容量计算
```python
self.max_capacity_per_slot = self.f_server * self.g_server * config.delta_t * self.max_load
```
公式：$Capacity = f_{server} \times g_{server} \times \Delta t \times load_{max}$

#### 4.1.3 任务接受判断
```python
def can_accept_task(self, flops_required):
    current_capacity_used = sum([task['flops'] for task in self.active_tasks])
    return (current_capacity_used + flops_required) <= self.max_capacity_per_slot
```

#### 4.1.4 负载更新
```python
def update_load(self):
    if len(self.active_tasks) == 0:
        self.current_load = 0.0
    else:
        total_flops = sum([task['flops'] for task in self.active_tasks])
        self.current_load = min(1.0, total_flops / self.max_capacity_per_slot)
```

### 4.2 ServerManager类
负责管理所有边缘服务器：
- `allocate_tasks()`: 任务分配到服务器
- `process_all_tasks()`: 处理所有服务器任务
- `check_capacity_constraints()`: 检查容量约束违反

## 5. 任务模型详解 (task_model.py)

### 5.1 ResNet50LayerInfo类

#### 5.1.1 层级信息结构
每个卷积层包含以下信息：
```python
{
    'layer_id': int,           # 层ID
    'layer_type': 'conv',      # 层类型
    'input_shape': [C,H,W],    # 输入形状
    'output_shape': [C,H,W],   # 输出形状
    'flops': float,            # 该层FLOPs
    'params': int,             # 参数数量
    'cumulative_flops': float, # 累积FLOPs
    'output_size': int         # 输出数据大小(bits)
}
```

#### 5.1.2 关键层级信息
- **Layer 0**: Conv2d, 输入[3,32,32]→输出[64,32,32], FLOPs=1.77M
- **Layer 32**: Exit_1前最后卷积层, 累积FLOPs=221.38M, 精度=0.77
- **Layer 73**: Exit_2前最后卷积层, 累积FLOPs=771.86M, 精度=0.8731
- **Layer 105**: Exit_3前最后卷积层, 累积FLOPs=1250.43M, 精度=0.9038
- **Layer 135**: Exit_4前最后卷积层, 累积FLOPs=1464.64M, 精度=0.9113
- **Layer 150**: Exit_5前最后卷积层, 累积FLOPs=1817.08M, 精度=0.9121
- **Layer 164**: 完整模型, 累积FLOPs=1942.99M, 精度=0.9133

### 5.2 EdgeInferenceTask类

#### 5.2.1 决策变量
```python
self.split_point = None    # p_k: 分割点层ID
self.exit_point = None     # e_k: 早退出点名称
self.frequency = None      # f_k: 本地CPU频率
self.target_server = None  # n_k: 目标服务器ID
```

#### 5.2.2 工作负载分布计算
```python
def compute_workload_distribution(self):
    if self.split_point == 0:
        # 完全本地执行
        return {
            'local_flops': total_flops,
            'edge_flops': 0,
            'communication_data': 0,
            'execution_mode': 'local_only'
        }
    elif self.split_point >= exit_layer_id:
        # 完全卸载
        return {
            'local_flops': 0,
            'edge_flops': total_flops,
            'communication_data': 3 * 32 * 32 * 8,  # 输入数据大小
            'execution_mode': 'edge_only'
        }
    else:
        # 分割执行
        local_flops = split_layer_info['cumulative_flops']
        edge_flops = total_flops - local_flops
        comm_data = split_layer_info['output_size']
        return {
            'local_flops': local_flops,
            'edge_flops': edge_flops,
            'communication_data': comm_data,
            'execution_mode': 'split_execution'
        }
```

### 5.3 TaskManager类
负责任务生命周期管理：
- `create_task()`: 创建新推理任务
- `create_batch_tasks()`: 批量创建任务
- `complete_task()`: 完成任务处理

## 6. 性能模型详解 (performance_model.py)

### 6.1 EnergyModel类

#### 6.1.1 计算能耗模型
```python
def compute_computation_energy(self, frequency, processing_time):
    frequency_ghz = frequency / 1e9
    return self.config.kappa * (frequency_ghz ** 3) * processing_time
```
公式：$E_{comp} = \kappa \cdot f^3 \cdot T_{local}$

#### 6.1.2 通信能耗模型
```python
def compute_communication_energy(self, P_tx, data_size, transmission_rate):
    transmission_time = data_size / transmission_rate
    return P_tx * transmission_time
```
公式：$E_{comm} = P_{tx} \cdot T_{comm} = P_{tx} \cdot \frac{D_{tx}}{R}$

#### 6.1.3 静态能耗模型
```python
def compute_static_energy(self, P_static, total_time):
    return P_static * total_time
```
公式：$E_{idle} = P_{static} \cdot T_{total}$

### 6.2 DelayModel类

#### 6.2.1 本地处理延迟
```python
def compute_local_processing_time(self, flops, frequency, g_device):
    if flops <= 0:
        return 0
    return flops / (frequency * g_device)
```
公式：$T_{local} = \frac{C_{local}}{f_m \cdot g_m}$

#### 6.2.2 通信延迟
```python
def compute_communication_time(self, data_size, transmission_rate):
    if data_size <= 0:
        return 0
    return data_size / transmission_rate
```
公式：$T_{comm} = \frac{D_{tx}}{R_{tx}}$

#### 6.2.3 边缘处理延迟
```python
def compute_edge_processing_time(self, flops, server_frequency, g_server):
    if flops <= 0:
        return 0
    return flops / (server_frequency * g_server)
```
公式：$T_{edge} = \frac{C_{edge}}{f_{server} \cdot g_{server}}$

#### 6.2.4 总延迟计算
根据执行模式计算总延迟：
- **本地执行**: $T_{total} = T_{local}$
- **边缘执行**: $T_{total} = T_{comm} + T_{edge}$
- **分割执行**: $T_{total} = T_{local} + T_{comm} + T_{edge}$

### 6.3 AdaptiveWeightMechanism类

#### 6.3.1 权重计算
```python
def calculate_weight(self, current_battery, max_battery):
    battery_ratio = current_battery / max_battery
    exponent = -self.beta * (battery_ratio - self.theta)
    exponent = np.clip(exponent, -500, 500)  # 防止数值溢出
    alpha = 1.0 / (1.0 + np.exp(exponent))
    return alpha
```

#### 6.3.2 权重语义解释
```python
def get_weight_interpretation(self, alpha):
    if alpha > 0.7:
        return "优先延迟最小化 (电量充足)"
    elif alpha > 0.3:
        return "平衡优化 (电量适中)"
    else:
        return "优先能耗最小化 (电量不足)"
```

### 6.4 PerformanceEvaluator类

#### 6.4.1 个体目标函数
```python
individual_objective = alpha * delay_info['total'] + (1 - alpha) * energy_info['total']
```
公式：$Obj_m = \alpha_m \cdot T_m + (1-\alpha_m) \cdot E_m$

#### 6.4.2 奖励函数设计
```python
def compute_reward(self, device_performances, constraint_violations):
    for perf in device_performances:
        # 获取原始性能指标
        delay = perf['delay_info']['total']  # 秒
        energy = perf['energy_info']['total']  # 焦耳
        alpha = perf['adaptive_weight']
        
        # 归一化性能指标
        delay_ms = delay * 1000
        normalized_delay = min(delay_ms / 150.0, 2.0)  # 150ms为参考
        normalized_energy = min(energy / 50.0, 2.0)    # 50J为参考
        
        # 基础性能奖励 (0-100分制)
        delay_score = max(0, 100 - normalized_delay * 50)
        energy_score = max(0, 100 - normalized_energy * 50)
        
        # 加权组合基础奖励
        base_reward = alpha * delay_score + (1 - alpha) * energy_score
        
        # 约束惩罚
        penalty = 0
        for violation in device_violations:
            if violation['type'] == 'battery_depletion':
                penalty += 50
            elif violation['type'] == 'delay_violation':
                penalty += 30
            elif violation['type'] == 'accuracy_violation':
                penalty += 40
            elif violation['type'] == 'frequency_violation':
                penalty += 20
        
        # 最终奖励
        individual_reward = base_reward - penalty
        individual_reward = max(individual_reward, -200)  # 最低-200分
        individual_reward = min(individual_reward, 100)   # 最高100分
```

#### 6.4.3 约束检查
```python
def _check_constraints(self, device, delay_info, task_info):
    violations = []
    
    # 电池约束
    if device.is_depleted():
        violations.append({'type': 'battery_depletion', ...})
    
    # 延迟约束
    if delay_info['total'] > self.config.T_max:
        violations.append({'type': 'delay_violation', ...})
    
    # 频率约束
    if not (device.f_min <= device.current_frequency <= device.f_max):
        violations.append({'type': 'frequency_violation', ...})
    
    # 精度约束
    if actual_accuracy < required_accuracy:
        violations.append({'type': 'accuracy_violation', ...})
    
    return violations
```

## 7. 网络模型详解 (network_model.py)

### 7.1 ActorNetwork类

#### 7.1.1 网络架构
```python
class ActorNetwork(nn.Module):
    def __init__(self, state_dim, action_dim, hidden_dims, dropout_rate=0.1):
        # 特征提取层
        self.feature_layers = nn.Sequential(
            nn.Linear(state_dim, hidden_dims[0]),  # 3 → 256
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[0], hidden_dims[1]),  # 256 → 128
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[1], hidden_dims[2]),  # 128 → 64
            nn.ReLU()
        )
        
        # 输出头
        self.split_point_head = nn.Linear(64, 50)  # 分割点选择
        self.frequency_head = nn.Linear(64, 1)     # 频率输出
        self.server_head = nn.Linear(64, 4)        # 服务器选择
```

#### 7.1.2 前向传播
```python
def forward(self, state):
    features = self.feature_layers(state)
    
    # 分割点选择 (离散动作)
    split_logits = self.split_point_head(features)
    split_probs = F.softmax(split_logits, dim=-1)
    
    # 频率选择 (连续动作)
    frequency_raw = self.frequency_head(features)
    frequency_normalized = torch.tanh(frequency_raw)  # [-1, 1]
    
    # 服务器选择 (离散动作)
    server_logits = self.server_head(features)
    server_probs = F.softmax(server_logits, dim=-1)
    
    return {
        'split_point_logits': split_logits,
        'split_point_probs': split_probs,
        'frequency_normalized': frequency_normalized,
        'server_logits': server_logits,
        'server_probs': server_probs
    }
```

#### 7.1.3 动作采样
```python
def sample_action(self, state, temperature=1.0, add_noise=True, noise_std=0.1):
    output = self.forward(state)
    
    # Gumbel-Softmax采样分割点
    split_point = self._gumbel_softmax_sample(
        output['split_point_logits'], temperature, hard=True
    )
    
    # 连续动作采样频率
    frequency = output['frequency_normalized']
    if add_noise:
        noise = torch.normal(0, noise_std, frequency.shape)
        frequency = torch.clamp(frequency + noise, -1, 1)
    
    # Gumbel-Softmax采样服务器
    server = self._gumbel_softmax_sample(
        output['server_logits'], temperature, hard=True
    )
    
    return {'split_point': split_point, 'frequency': frequency, 'server': server}
```

#### 7.1.4 Gumbel-Softmax采样
```python
def _gumbel_softmax_sample(self, logits, temperature, hard=False):
    gumbel_noise = -torch.log(-torch.log(torch.rand_like(logits) + 1e-20) + 1e-20)
    y = (logits + gumbel_noise) / temperature
    y_soft = F.softmax(y, dim=-1)
    
    if hard:
        # 硬采样：one-hot
        index = y_soft.argmax(dim=-1, keepdim=True)
        y_hard = torch.zeros_like(y_soft).scatter_(-1, index, 1.0)
        return y_hard - y_soft.detach() + y_soft
    else:
        return y_soft
```

### 7.2 CriticNetwork类

#### 7.2.1 网络架构
```python
class CriticNetwork(nn.Module):
    def __init__(self, global_state_dim, global_action_dim, hidden_dims):
        # 状态编码器
        self.state_encoder = nn.Sequential(
            nn.Linear(global_state_dim, hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[0], hidden_dims[1]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[1], hidden_dims[2]),
            nn.ReLU()
        )
        
        # 动作编码器
        self.action_encoder = nn.Sequential(
            nn.Linear(global_action_dim, hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[0], hidden_dims[1]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[1], hidden_dims[2]),
            nn.ReLU()
        )
        
        # 融合层
        fusion_dim = hidden_dims[2] + hidden_dims[2]
        self.fusion_layers = nn.Sequential(
            nn.Linear(fusion_dim, hidden_dims[2]),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dims[2], hidden_dims[2] // 2),
            nn.ReLU(),
            nn.Linear(hidden_dims[2] // 2, 1)
        )
```

#### 7.2.2 前向传播
```python
def forward(self, global_state, global_action):
    # 编码状态和动作
    state_features = self.state_encoder(global_state)
    action_features = self.action_encoder(global_action)
    
    # 融合特征
    fused_features = torch.cat([state_features, action_features], dim=-1)
    
    # 输出Q值
    q_value = self.fusion_layers(fused_features)
    return q_value
```

### 7.3 MADDPGAgent类

#### 7.3.1 智能体初始化
```python
class MADDPGAgent:
    def __init__(self, agent_id, state_dim, action_dim, global_state_dim, 
                 global_action_dim, config, device='cpu'):
        # 创建网络
        self.actor = ActorNetwork(state_dim, action_dim, config.hidden_dims)
        self.critic = CriticNetwork(global_state_dim, global_action_dim, config.hidden_dims)
        
        # 创建目标网络
        self.target_actor = ActorNetwork(state_dim, action_dim, config.hidden_dims)
        self.target_critic = CriticNetwork(global_state_dim, global_action_dim, config.hidden_dims)
        
        # 初始化目标网络
        self._hard_update(self.target_actor, self.actor)
        self._hard_update(self.target_critic, self.critic)
        
        # 优化器
        self.actor_optimizer = torch.optim.Adam(self.actor.parameters(), lr=config.lr_actor)
        self.critic_optimizer = torch.optim.Adam(self.critic.parameters(), lr=config.lr_critic)
```

#### 7.3.2 动作选择
```python
def select_action(self, state, add_noise=True):
    state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
    
    action_dict = self.actor.sample_action(
        state_tensor, 
        temperature=1.0, 
        add_noise=add_noise, 
        noise_std=self.noise_std
    )
    
    # 转换为numpy数组
    result = {}
    for key, value in action_dict.items():
        if isinstance(value, torch.Tensor):
            result[key] = value.cpu().numpy().squeeze(0)
        else:
            result[key] = value
    
    return result
```

#### 7.3.3 噪声更新
```python
def update_noise(self):
    self.noise_std *= self.noise_decay
    self.noise_std = max(self.noise_std, self.config.final_noise)
```

## 8. MADDPG算法详解 (maddpg_algorithm.py)

### 8.1 ReplayBuffer类

#### 8.1.1 经验存储
```python
class ReplayBuffer:
    def __init__(self, capacity):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
    
    def push(self, experience):
        self.buffer.append(experience)
    
    def sample(self, batch_size, device='cpu'):
        batch = random.sample(self.buffer, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)
        
        return {
            'states': torch.FloatTensor(np.array(states)).to(device),
            'actions': torch.FloatTensor(np.array(actions)).to(device),
            'rewards': torch.FloatTensor(np.array(rewards)).to(device),
            'next_states': torch.FloatTensor(np.array(next_states)).to(device),
            'dones': torch.FloatTensor(np.array(dones)).to(device)
        }
```

### 8.2 MADDPGAlgorithm类

#### 8.2.1 维度定义
```python
self.local_state_dim = 3      # [battery_ratio, frequency_ratio, absolute_battery]
self.local_action_dim = 3     # [split_point_idx, frequency, server_idx]
self.global_state_dim = self.M * self.local_state_dim + config.N * 4
self.global_action_dim = self.M * self.local_action_dim
```

#### 8.2.2 动作选择
```python
def select_actions(self, local_states, global_state, add_noise=True):
    actions = []
    global_action = []
    
    for i, agent in enumerate(self.agents):
        # 选择动作
        action_dict = agent.select_action(local_states[i], add_noise)
        actions.append(action_dict)
        
        # 构建全局动作向量
        agent_action = self._convert_action_dict_to_vector(action_dict)
        global_action.extend(agent_action)
    
    return actions, np.array(global_action)
```

#### 8.2.3 动作转换
```python
def _convert_action_dict_to_vector(self, action_dict):
    # 分割点：取argmax索引
    split_point_idx = np.argmax(action_dict['split_point'])
    
    # 频率：归一化值
    frequency = action_dict['frequency'].item() if hasattr(action_dict['frequency'], 'item') else action_dict['frequency']
    
    # 服务器：取argmax索引
    server_idx = np.argmax(action_dict['server'])
    
    return [split_point_idx, frequency, server_idx]
```

#### 8.2.4 经验存储
```python
def store_experience(self, local_states, global_state, actions, global_action, 
                    rewards, next_local_states, next_global_state, dones):
    experience = {
        'local_states': np.array(local_states),
        'global_state': global_state,
        'global_action': global_action,
        'rewards': rewards,
        'next_local_states': np.array(next_local_states),
        'next_global_state': next_global_state,
        'dones': dones
    }
    
    self.replay_buffer.push((
        experience['local_states'],
        experience['global_action'],
        experience['rewards'],
        experience['next_local_states'],
        experience['dones']
    ))
```

#### 8.2.5 智能体更新
```python
def update_agents(self):
    if len(self.replay_buffer) < self.config.batch_size:
        return None
    
    # 采样批次数据
    batch = self.replay_buffer.sample(self.config.batch_size, self.device)
    
    # 准备训练数据
    batch_data = self._prepare_batch_data(batch)
    
    actor_losses = []
    critic_losses = []
    
    # 更新每个智能体
    for agent in self.agents:
        # 更新Critic
        critic_loss = agent.update_critic(batch_data)
        critic_losses.append(critic_loss)
        
        # 更新Actor
        actor_loss = agent.update_actor(batch_data)
        actor_losses.append(actor_loss)
        
        # 软更新目标网络
        agent.soft_update()
    
    # 更新探索噪声
    for agent in self.agents:
        agent.update_noise()
    
    return {
        'actor_loss': np.mean(actor_losses),
        'critic_loss': np.mean(critic_losses)
    }
```

## 9. 环境模型详解 (environment.py)

### 9.1 EdgeInferenceEnvironment类

#### 9.1.1 环境初始化
```python
class EdgeInferenceEnvironment:
    def __init__(self, config):
        self.config = config
        
        # 初始化各个组件
        self.device_manager = DeviceManager(config)
        self.server_manager = ServerManager(config)
        self.task_manager = TaskManager(config)
        self.performance_evaluator = PerformanceEvaluator(config)
```

#### 9.1.2 状态获取
```python
def get_state(self):
    # 获取设备状态
    device_states = self.device_manager.get_all_states()  # [M, 3]
    
    # 获取服务器状态
    server_states = self.server_manager.get_all_states()  # [N, 4]
    
    # 构建全局状态
    global_state = np.concatenate([
        device_states.flatten(),
        server_states.flatten()
    ])
    
    return {
        'local_states': device_states,
        'global_state': global_state,
        'server_states': server_states
    }
```

#### 9.1.3 环境重置
```python
def reset(self, acc_min_list=None):
    # 重置所有组件
    self.device_manager.reset_all()
    self.server_manager.reset_all()
    self.task_manager.reset()
    
    # 生成精度要求
    if acc_min_list is None:
        acc_min_list = []
        for _ in range(self.config.M):
            acc_min = np.random.uniform(*self.config.acc_min_range)
            acc_min_list.append(acc_min)
    
    # 创建任务
    self.current_tasks = self.task_manager.create_batch_tasks(
        self.config.M, acc_min_list
    )
    
    return self.get_state()
```

#### 9.1.4 环境步进
```python
def step(self, actions):
    # 解析动作并设置任务决策变量
    task_allocations = self._parse_actions(actions)
    
    # 执行任务分配和处理
    execution_results = self._execute_tasks(task_allocations)
    
    # 计算性能指标
    performance_results = self.performance_evaluator.evaluate_system_performance(
        self.device_manager.devices,
        self.server_manager.servers,
        execution_results['tasks_info']
    )
    
    # 更新设备电池
    device_energies = [perf['energy_info']['total'] for perf in performance_results['device_performances']]
    self.device_manager.update_all_batteries(device_energies)
    
    # 计算奖励
    rewards = self.performance_evaluator.compute_reward(
        performance_results['device_performances'],
        performance_results['constraint_violations']
    )
    
    # 检查结束条件
    dones = self._check_done_conditions()
    
    # 获取下一状态
    next_state = self.get_state()
    
    return next_state, rewards, dones, info
```

#### 9.1.5 动作解析
```python
def _parse_actions(self, actions):
    task_allocations = []
    
    for i, (action_dict, task) in enumerate(zip(actions, self.current_tasks)):
        # 解析动作
        split_point_idx = np.argmax(action_dict['split_point'])
        frequency_normalized = action_dict['frequency'].item()
        server_idx = np.argmax(action_dict['server'])
        
        # 转换频率到实际值
        device = self.device_manager.devices[i]
        frequency = device.f_min + (frequency_normalized + 1) / 2 * (device.f_max - device.f_min)
        frequency = np.clip(frequency, device.f_min, device.f_max)
        
        # 获取有效分割点
        valid_split_points = task.get_valid_split_points()
        if valid_split_points and split_point_idx < len(valid_split_points):
            split_point = valid_split_points[split_point_idx]
        else:
            split_point = valid_split_points[0] if valid_split_points else 0
        
        # 设置任务决策变量
        task.set_decision_variables(split_point, frequency, server_idx)
        
        # 更新设备频率
        device.set_frequency(frequency)
        
        task_allocations.append({
            'device_id': i,
            'task_id': task.task_id,
            'split_point': split_point,
            'frequency': frequency,
            'server_id': server_idx,
            'task': task
        })
    
    return task_allocations
```

#### 9.1.6 Episode运行
```python
def run_episode(self, maddpg_algorithm, max_steps=None, train=True):
    if max_steps is None:
        max_steps = self.config.tasks_per_episode
    
    # 重置环境
    state = self.reset()
    
    episode_rewards = []
    episode_info = []
    
    for step in range(max_steps):
        # 选择动作
        actions, global_action = maddpg_algorithm.select_actions(
            state['local_states'], 
            state['global_state'], 
            add_noise=train
        )
        
        # 执行动作
        next_state, rewards, dones, info = self.step(actions)
        
        # 存储经验（仅在训练模式下）
        if train:
            maddpg_algorithm.store_experience(
                state['local_states'],
                state['global_state'],
                actions,
                global_action,
                rewards,
                next_state['local_states'],
                next_state['global_state'],
                dones
            )
            
            # 更新网络
            if (step + 1) % self.config.update_frequency == 0 and len(maddpg_algorithm.replay_buffer) >= self.config.warmup_steps:
                update_info = maddpg_algorithm.update_agents()
        
        # 记录数据
        episode_rewards.append(rewards)
        episode_info.append(info)
        
        # 更新状态
        state = next_state
        
        # 检查结束条件
        if np.all(dones):
            break
    
    # 计算episode统计
    episode_stats = self._compute_episode_statistics(episode_rewards, episode_info)
    
    return {
        'episode_rewards': episode_rewards,
        'episode_info': episode_info,
        'episode_stats': episode_stats
    }
```

## 10. 主训练脚本详解 (main.py)

### 10.1 TrainingManager类

#### 10.1.1 训练管理器初始化
```python
class TrainingManager:
    def __init__(self, config, device='cpu'):
        self.config = config
        self.device = device
        
        # 创建环境和算法
        self.env = EdgeInferenceEnvironment(config)
        self.maddpg = MADDPGAlgorithm(config, device)
        
        # 训练统计
        self.training_history = {
            'episode_rewards': [],
            'system_metrics': [],
            'constraint_violations': [],
            'battery_levels': [],
            'actor_losses': [],
            'critic_losses': []
        }
```

#### 10.1.2 训练主循环
```python
def train(self, num_episodes=None, save_interval=50, render_interval=10):
    if num_episodes is None:
        num_episodes = self.config.num_episodes
    
    for episode in range(num_episodes):
        # 运行一个episode
        episode_result = self.env.run_episode(
            self.maddpg, 
            max_steps=self.config.tasks_per_episode,
            train=True
        )
        
        # 记录训练数据
        self._record_episode_data(episode, episode_result)
        
        # 打印进度
        if (episode + 1) % render_interval == 0:
            self._print_progress(episode, episode_result, episode_start_time)
        
        # 保存模型
        if (episode + 1) % save_interval == 0:
            self._save_checkpoint(episode + 1)
    
    # 保存最终结果
    self._save_final_results()
    
    # 生成训练报告
    self._generate_training_report()
```

#### 10.1.3 数据记录
```python
def _record_episode_data(self, episode, episode_result):
    stats = episode_result['episode_stats']
    
    # 记录奖励
    self.training_history['episode_rewards'].append(stats['system_total_reward'])
    
    # 记录系统指标
    self.training_history['system_metrics'].append({
        'episode': episode,
        'average_energy': stats['average_energy'],
        'average_delay': stats['average_delay'],
        'system_mean_reward': stats['system_mean_reward']
    })
    
    # 记录约束违反
    self.training_history['constraint_violations'].append(stats['total_constraint_violations'])
    
    # 记录电池电量
    self.training_history['battery_levels'].append(stats['final_battery_levels'])
```

#### 10.1.4 进度打印
```python
def _print_progress(self, episode, episode_result, episode_start_time):
    stats = episode_result['episode_stats']
    episode_time = time.time() - episode_start_time
    
    print(f"\nEpisode {episode + 1}/{self.config.num_episodes}:")
    print(f"  时间: {episode_time:.2f}s")
    print(f"  系统总奖励: {stats['system_total_reward']:.4f}")
    print(f"  平均能耗: {stats['average_energy']:.4f}J")
    print(f"  平均延迟: {stats['average_delay']:.6f}s")
    print(f"  约束违反: {stats['total_constraint_violations']}")
    print(f"  电池耗尽设备: {stats['battery_depletion_count']}/{self.config.M}")
```

#### 10.1.5 模型评估
```python
def evaluate(self, num_episodes=10, load_model_path=None):
    if load_model_path:
        self.maddpg.load_models(load_model_path)
    
    # 设置为评估模式
    self.maddpg.set_eval_mode()
    
    eval_results = []
    
    for episode in range(num_episodes):
        # 运行episode（不训练）
        episode_result = self.env.run_episode(
            self.maddpg,
            max_steps=self.config.tasks_per_episode,
            train=False
        )
        
        eval_results.append(episode_result['episode_stats'])
    
    # 计算评估统计
    eval_stats = self._compute_evaluation_statistics(eval_results)
    
    return eval_stats
```

#### 10.1.6 训练报告生成
```python
def _generate_training_report(self):
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('MADDPG训练结果', fontsize=16)
    
    # 奖励曲线
    axes[0, 0].plot(self.training_history['episode_rewards'])
    axes[0, 0].set_title('Episode奖励变化')
    
    # 能耗和延迟
    episodes = [m['episode'] for m in self.training_history['system_metrics']]
    energies = [m['average_energy'] for m in self.training_history['system_metrics']]
    delays = [m['average_delay'] for m in self.training_history['system_metrics']]
    
    axes[0, 1].plot(episodes, energies, label='平均能耗 (J)')
    ax2 = axes[0, 1].twinx()
    ax2.plot(episodes, delays, 'r-', label='平均延迟 (s)')
    
    # 约束违反
    axes[1, 0].plot(self.training_history['constraint_violations'])
    axes[1, 0].set_title('约束违反次数')
    
    # 网络损失
    axes[1, 1].plot(self.training_history['actor_losses'], label='Actor Loss')
    axes[1, 1].plot(self.training_history['critic_losses'], label='Critic Loss')
    axes[1, 1].set_title('网络训练损失')
    
    plt.tight_layout()
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
```

### 10.2 主函数
```python
def main():
    # 创建配置
    config = Config()
    
    # 检查CUDA可用性
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建训练管理器
    trainer = TrainingManager(config, device)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    trainer.env.seed(42)
    
    try:
        # 开始训练
        trainer.train(
            num_episodes=config.num_episodes,
            save_interval=50,
            render_interval=10
        )
        
        # 评估最终模型
        eval_stats = trainer.evaluate(num_episodes=20)
        
    except KeyboardInterrupt:
        # 保存当前进度
        trainer._save_checkpoint(trainer.env.current_episode)
```

## 11. 算法流程总结

### 11.1 训练流程
1. **初始化**：创建环境、MADDPG算法、设备和服务器
2. **Episode循环**：
   - 重置环境，生成新任务
   - **Step循环**：
     - 获取当前状态（设备状态+服务器状态）
     - 各智能体选择动作（分割点、频率、服务器）
     - 执行动作，计算性能指标
     - 计算奖励，更新设备电池
     - 存储经验到回放缓冲区
     - 定期更新网络参数
   - 记录episode统计数据
3. **训练完成**：保存模型，生成训练报告

### 11.2 决策流程
对于每个推理任务，智能体需要决策：
1. **早退出点选择**：根据精度要求自动选择
2. **分割点选择**：在有效分割点中选择（Actor网络输出）
3. **频率选择**：设置本地CPU频率（连续动作）
4. **服务器选择**：选择目标边缘服务器（离散动作）

### 11.3 性能评估流程
1. **工作负载分布计算**：根据分割点计算本地和边缘计算量
2. **延迟计算**：本地处理时间+通信时间+边缘处理时间
3. **能耗计算**：计算能耗+通信能耗+静态能耗
4. **自适应权重计算**：根据电池电量计算权重
5. **目标函数计算**：加权组合延迟和能耗
6. **奖励计算**：基于性能分数和约束惩罚

### 11.4 约束处理
系统考虑以下约束：
- **延迟约束**：$T_{total} \leq T_{max}$
- **电池约束**：$B_m \geq B_{min}$
- **精度约束**：$Acc \geq Acc_{min}$
- **频率约束**：$f_{min} \leq f_m \leq f_{max}$
- **服务器容量约束**：$\sum FLOPs \leq Capacity$

## 12. 关键公式汇总

### 12.1 性能模型公式
- **本地处理时间**：$T_{local} = \frac{C_{local}}{f_m \cdot g_m}$
- **通信时间**：$T_{comm} = \frac{D_{tx}}{R_{tx}}$
- **边缘处理时间**：$T_{edge} = \frac{C_{edge}}{f_{server} \cdot g_{server}}$
- **计算能耗**：$E_{comp} = \kappa \cdot f^3 \cdot T_{local}$
- **通信能耗**：$E_{comm} = P_{tx} \cdot T_{comm}$
- **静态能耗**：$E_{idle} = P_{static} \cdot T_{total}$

### 12.2 自适应权重公式
- **自适应权重**：$\alpha_m = \frac{1}{1 + \exp(-\beta \cdot (\frac{B_m}{B_{max}} - \theta))}$
- **个体目标函数**：$Obj_m = \alpha_m \cdot T_m + (1-\alpha_m) \cdot E_m$
- **系统目标函数**：$Obj_{system} = \frac{1}{M} \sum_{m=1}^{M} Obj_m$

### 12.3 奖励函数公式
- **延迟分数**：$Score_{delay} = \max(0, 100 - \frac{T_{ms}}{150} \times 50)$
- **能耗分数**：$Score_{energy} = \max(0, 100 - \frac{E_J}{50} \times 50)$
- **基础奖励**：$R_{base} = \alpha \cdot Score_{delay} + (1-\alpha) \cdot Score_{energy}$
- **最终奖励**：$R_{final} = \max(-200, \min(100, R_{base} - Penalty))$

### 12.4 MADDPG更新公式

#### 12.4.1 Critic更新
- **目标Q值**：$y_i = r_i + \gamma Q_{\mu'_i}(s'_i, a'_1, ..., a'_M)$
- **Critic损失**：$L(\theta_i) = \frac{1}{N} \sum (y_i - Q_{\mu_i}(s_i, a_1, ..., a_M))^2$
- **梯度更新**：$\theta_i \leftarrow \theta_i - \alpha_{critic} \nabla_{\theta_i} L(\theta_i)$

#### 12.4.2 Actor更新
- **策略梯度**：$\nabla_{\phi_i} J = \frac{1}{N} \sum \nabla_{a_i} Q_{\mu_i}(s, a_1, ..., a_M)|_{a_i=\mu_i(s_i)} \nabla_{\phi_i} \mu_i(s_i)$
- **参数更新**：$\phi_i \leftarrow \phi_i + \alpha_{actor} \nabla_{\phi_i} J$

#### 12.4.3 软更新
- **目标网络更新**：$\theta'_i \leftarrow \tau \theta_i + (1-\tau) \theta'_i$
- **其中**：$\tau = 0.005$ (软更新系数)

### 12.5 频率转换公式
- **归一化到实际频率**：$f_{actual} = f_{min} + \frac{f_{normalized} + 1}{2} \times (f_{max} - f_{min})$
- **其中**：$f_{normalized} \in [-1, 1]$ (Actor网络输出)

### 12.6 电池转换公式
- **能耗转换为电量消耗**：$\Delta B_{mAh} = \frac{E_J \times 1000}{V \times 3600}$
- **其中**：$V = 5V$ (工作电压), $E_J$ (能耗，焦耳)

## 13. 系统特性分析

### 13.1 多智能体协作机制
- **去中心化执行**：每个设备独立决策，基于本地状态
- **中心化训练**：Critic网络使用全局状态和动作信息
- **信息共享**：通过全局状态向量共享设备和服务器信息
- **协调优化**：通过联合动作空间实现系统级优化

### 13.2 自适应机制
- **电量感知**：根据电池电量动态调整优化目标
- **负载均衡**：考虑服务器负载进行任务分配
- **精度自适应**：根据精度要求选择合适的早退出点
- **噪声衰减**：训练过程中逐渐减少探索噪声

### 13.3 约束处理策略
- **软约束**：通过奖励函数中的惩罚项处理
- **硬约束**：通过动作空间限制和状态检查
- **多重约束**：同时考虑延迟、能耗、精度、电量等约束
- **约束优先级**：精度约束 > 延迟约束 > 能耗约束

### 13.4 异构性支持
- **设备异构**：不同的CPU频率范围、电池容量、通信能力
- **服务器异构**：不同的计算能力、负载容量
- **任务异构**：不同的精度要求、计算复杂度
- **网络异构**：不同的传输速率、传输功率

## 14. 实验配置说明

### 14.1 硬件配置
- **移动设备**：模拟Jetson Xavier设备
  - CPU频率：0.3-1.2 GHz
  - 电池容量：100-150 mAh
  - 计算能力：153 FLOPs/cycle
- **边缘服务器**：模拟Laptop RTX 4060
  - 等效频率：2.2-2.8 GHz
  - 计算能力：380 FLOPs/cycle
  - 最大负载：75%

### 14.2 网络配置
- **传输功率**：0.093-0.115 W
- **传输速率**：50-100 Mbps
- **静态功耗**：3-8 W
- **能耗系数**：κ = 1.3 W/GHz³

### 14.3 任务配置
- **模型**：ResNet-50 on CIFAR-10
- **早退出点**：6个（包括完整模型）
- **精度范围**：0.70-0.91
- **延迟约束**：150ms
- **分割点**：50个候选卷积层

### 14.4 训练配置
- **Episode数**：200
- **每Episode任务数**：150
- **批次大小**：32
- **学习率**：Actor 1e-5, Critic 5e-5
- **缓冲区大小**：4000
- **更新频率**：每2步更新一次

## 15. 性能指标说明

### 15.1 系统级指标
- **系统总奖励**：所有设备所有步骤的奖励累积
- **平均能耗**：所有设备的平均能耗 (焦耳)
- **平均延迟**：所有设备的平均延迟 (秒)
- **约束违反次数**：总的约束违反数量
- **电池耗尽设备数**：电量低于5%的设备数量

### 15.2 设备级指标
- **个体奖励**：单个设备的奖励值
- **电量消耗比例**：电量消耗占初始电量的比例
- **频率选择分布**：CPU频率的选择统计
- **分割点选择分布**：分割点的选择统计
- **服务器选择分布**：目标服务器的选择统计

### 15.3 训练指标
- **Actor损失**：策略网络的训练损失
- **Critic损失**：价值网络的训练损失
- **探索噪声**：当前的噪声标准差
- **缓冲区利用率**：经验回放缓冲区的使用情况

## 16. 文件结构说明

```
final5/
├── config.py              # 系统配置参数
├── device_model.py         # 移动设备模型
├── server_model.py         # 边缘服务器模型
├── task_model.py           # 推理任务模型
├── performance_model.py    # 性能评估模型
├── network_model.py        # 神经网络模型
├── maddpg_algorithm.py     # MADDPG算法实现
├── environment.py          # 环境管理器
├── main.py                 # 主训练脚本
├── requirements.txt        # 依赖包列表
├── resnet50_cifar_accuracy.txt    # 精度数据
├── resnet50_cifar_analysis.txt    # 模型分析数据
└── training_results/       # 训练结果目录
    ├── checkpoint_ep*/     # 检查点目录
    ├── final_models_*      # 最终模型文件
    ├── final_training_history.json  # 训练历史
    └── training_progress_*.png      # 训练图表
```

## 17. 使用说明

### 17.1 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 检查CUDA可用性（可选）
python -c "import torch; print(torch.cuda.is_available())"
```

### 17.2 开始训练
```bash
# 运行训练
python main.py

# 训练过程中会自动：
# 1. 每10个episode打印进度
# 2. 每50个episode保存检查点
# 3. 训练结束后生成报告和图表
```

### 17.3 参数调整
修改 `config.py` 中的参数：
- 调整学习率：`lr_actor`, `lr_critic`
- 调整网络结构：`hidden_dims`
- 调整训练参数：`num_episodes`, `batch_size`
- 调整约束参数：`T_max`, `beta`, `theta`

### 17.4 结果分析
训练完成后查看：
- `training_results/final_training_history.json`：详细训练数据
- `training_results/training_progress_*.png`：训练曲线图
- 终端输出：实时训练进度和统计信息

## 18. 总结

本框架实现了一个完整的基于MADDPG的异构多边缘服务器协作深度神经网络推理优化系统。框架的主要创新点包括：

1. **自适应权重机制**：根据设备电量动态平衡能耗与延迟优化
2. **多层早退出支持**：支持ResNet-50的6个早退出点以平衡精度和计算量
3. **异构环境建模**：考虑设备和服务器的性能差异
4. **多重约束处理**：同时处理延迟、能耗、精度、电量等多种约束
5. **去中心化执行**：每个设备独立决策，提高系统鲁棒性

框架通过详细的数学建模、完整的实现代码和丰富的实验配置，为边缘计算环境下的深度学习推理优化提供了一个可靠的解决方案。