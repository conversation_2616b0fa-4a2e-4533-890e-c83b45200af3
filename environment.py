"""
环境管理器
包含边缘推理环境、状态管理、动作执行、奖励计算、Episode运行等功能
"""

import numpy as np
import time
from typing import List, Dict, Tuple, Optional
from device_model import DeviceManager
from server_model import ServerManager
from task_model import TaskManager
from performance_model import PerformanceEvaluator

class EdgeInferenceEnvironment:
    """边缘推理环境类"""
    
    def __init__(self, config):
        self.config = config
        
        # 初始化各个组件
        self.device_manager = DeviceManager(config)
        self.server_manager = ServerManager(config)
        self.task_manager = TaskManager(config)
        self.performance_evaluator = PerformanceEvaluator(config)
        
        # 环境状态
        self.current_tasks = []
        self.current_episode = 0
        self.current_step = 0
        
        # 统计信息
        self.episode_history = []
        self.step_history = []
    
    def reset(self, acc_min_list: Optional[List[float]] = None) -> Dict:
        """重置环境"""
        # 重置所有组件
        self.device_manager.reset_all()
        self.server_manager.reset_all()
        self.task_manager.reset()
        
        # 重置步数
        self.current_step = 0
        
        # 生成精度要求
        if acc_min_list is None:
            acc_min_list = []
            for _ in range(self.config.M):
                acc_min = np.random.uniform(*self.config.acc_min_range)
                acc_min_list.append(acc_min)
        
        # 创建任务
        self.current_tasks = self.task_manager.create_batch_tasks(
            self.config.M, acc_min_list
        )
        
        return self.get_state()
    
    def get_state(self) -> Dict:
        """获取环境状态"""
        # 获取设备状态
        device_states = self.device_manager.get_all_states()  # [M, 3]

        # 获取服务器状态
        server_states = self.server_manager.get_all_states()  # [N, 4]

        # 构建全局状态
        global_state = np.concatenate([
            device_states.flatten(),
            server_states.flatten()
        ])

        # 调试信息
        if hasattr(self, '_debug_state_printed') and not self._debug_state_printed:
            print(f"状态维度调试:")
            print(f"  设备状态形状: {device_states.shape}")
            print(f"  服务器状态形状: {server_states.shape}")
            print(f"  全局状态形状: {global_state.shape}")
            print(f"  期望全局状态维度: {self.config.M * 3 + self.config.N * 4}")
            self._debug_state_printed = True

        return {
            'local_states': device_states,
            'global_state': global_state,
            'server_states': server_states,
            'device_states': device_states
        }
    
    def step(self, actions: List[Dict]) -> Tuple[Dict, List[float], List[bool], Dict]:
        """环境步进"""
        self.current_step += 1
        
        # 解析动作并设置任务决策变量
        task_allocations = self._parse_actions(actions)
        
        # 执行任务分配和处理
        execution_results = self._execute_tasks(task_allocations)
        
        # 计算性能指标
        performance_results = self.performance_evaluator.evaluate_system_performance(
            self.device_manager.devices,
            self.server_manager.servers,
            execution_results['tasks_info']
        )
        
        # 更新设备电池
        device_energies = [perf['energy_info']['total'] for perf in performance_results['device_performances']]
        self.device_manager.update_all_batteries(device_energies)
        
        # 计算奖励
        rewards = self.performance_evaluator.compute_reward(
            performance_results['device_performances'],
            performance_results['constraint_violations']
        )
        
        # 检查结束条件
        dones = self._check_done_conditions()
        
        # 获取下一状态
        next_state = self.get_state()
        
        # 构建信息字典
        info = {
            'performance_results': performance_results,
            'execution_results': execution_results,
            'task_allocations': task_allocations,
            'step': self.current_step,
            'episode': self.current_episode
        }
        
        # 记录步骤历史
        self.step_history.append({
            'step': self.current_step,
            'rewards': rewards.copy(),
            'dones': dones.copy(),
            'system_metrics': performance_results['system_metrics'],
            'constraint_violations': len(performance_results['constraint_violations'])
        })
        
        return next_state, rewards, dones, info
    
    def _parse_actions(self, actions: List[Dict]) -> List[Dict]:
        """解析动作并设置任务决策变量"""
        task_allocations = []
        
        for i, (action_dict, task) in enumerate(zip(actions, self.current_tasks)):
            # 解析动作
            split_point_idx = np.argmax(action_dict['split_point'])
            frequency_normalized = action_dict['frequency'].item() if hasattr(action_dict['frequency'], 'item') else action_dict['frequency']
            server_idx = np.argmax(action_dict['server'])
            
            # 转换频率到实际值
            device = self.device_manager.devices[i]
            frequency = device.f_min + (frequency_normalized + 1) / 2 * (device.f_max - device.f_min)
            frequency = np.clip(frequency, device.f_min, device.f_max)
            
            # 获取有效分割点
            valid_split_points = task.get_valid_split_points()
            if valid_split_points and split_point_idx < len(valid_split_points):
                split_point = valid_split_points[split_point_idx]
            else:
                split_point = valid_split_points[0] if valid_split_points else 0
            
            # 设置任务决策变量
            task.set_decision_variables(split_point, frequency, server_idx)
            
            # 更新设备频率
            device.set_frequency(frequency)
            
            task_allocations.append({
                'device_id': i,
                'task_id': task.task_id,
                'split_point': split_point,
                'frequency': frequency,
                'server_id': server_idx,
                'task': task
            })
        
        return task_allocations
    
    def _execute_tasks(self, task_allocations: List[Dict]) -> Dict:
        """执行任务分配和处理"""
        # 分配任务到服务器
        allocation_results = self.server_manager.allocate_tasks(task_allocations)
        
        # 处理所有服务器任务
        processing_results = self.server_manager.process_all_tasks()
        
        # 构建任务信息
        tasks_info = []
        for allocation in task_allocations:
            task_info = allocation['task'].get_task_info()
            task_info.update({
                'device_id': allocation['device_id'],
                'allocated_server_id': allocation['server_id'],
                'split_point': allocation['split_point'],
                'frequency': allocation['frequency'],
                'target_server': allocation['server_id']
            })
            tasks_info.append(task_info)
        
        return {
            'allocation_results': allocation_results,
            'processing_results': processing_results,
            'tasks_info': tasks_info
        }
    
    def _check_done_conditions(self) -> List[bool]:
        """检查结束条件"""
        dones = []
        
        for device in self.device_manager.devices:
            # 检查电池是否耗尽
            done = device.is_depleted()
            dones.append(done)
        
        return dones
    
    def run_episode(self, maddpg_algorithm, max_steps: Optional[int] = None, train: bool = True) -> Dict:
        """运行一个完整的Episode"""
        if max_steps is None:
            max_steps = self.config.tasks_per_episode
        
        # 重置环境
        state = self.reset()
        
        episode_rewards = []
        episode_info = []
        
        for step in range(max_steps):
            # 选择动作
            actions, global_action = maddpg_algorithm.select_actions(
                state['local_states'], 
                state['global_state'], 
                add_noise=train
            )
            
            # 执行动作
            next_state, rewards, dones, info = self.step(actions)
            
            # 存储经验（仅在训练模式下）
            if train:
                maddpg_algorithm.store_experience(
                    state['local_states'],
                    state['global_state'],
                    actions,
                    global_action,
                    rewards,
                    next_state['local_states'],
                    next_state['global_state'],
                    dones
                )
                
                # 更新网络
                if (step + 1) % self.config.update_frequency == 0 and len(maddpg_algorithm.replay_buffer) >= self.config.warmup_steps:
                    update_info = maddpg_algorithm.update_agents()
                    if update_info:
                        info['update_info'] = update_info
            
            # 记录数据
            episode_rewards.append(rewards)
            episode_info.append(info)
            
            # 更新状态
            state = next_state
            
            # 检查结束条件
            if np.all(dones):
                break
        
        # 计算episode统计
        episode_stats = self._compute_episode_statistics(episode_rewards, episode_info)
        
        # 更新episode计数
        self.current_episode += 1
        
        # 记录episode历史
        self.episode_history.append({
            'episode': self.current_episode - 1,
            'steps': len(episode_rewards),
            'total_reward': episode_stats['system_total_reward'],
            'average_reward': episode_stats['system_mean_reward'],
            'final_battery_levels': episode_stats['final_battery_levels'],
            'constraint_violations': episode_stats['total_constraint_violations']
        })
        
        return {
            'episode_rewards': episode_rewards,
            'episode_info': episode_info,
            'episode_stats': episode_stats
        }
    
    def _compute_episode_statistics(self, episode_rewards: List[List[float]], episode_info: List[Dict]) -> Dict:
        """计算episode统计信息"""
        # 奖励统计
        all_rewards = np.array(episode_rewards)
        system_total_reward = np.sum(all_rewards)
        system_mean_reward = np.mean(all_rewards)
        device_total_rewards = np.sum(all_rewards, axis=0)
        device_mean_rewards = np.mean(all_rewards, axis=0)
        
        # 性能统计
        final_info = episode_info[-1] if episode_info else {}
        final_performance = final_info.get('performance_results', {})
        final_system_metrics = final_performance.get('system_metrics', {})
        
        # 约束违反统计
        all_violations = []
        for info in episode_info:
            violations = info.get('performance_results', {}).get('constraint_violations', [])
            if isinstance(violations, list):
                all_violations.extend(violations)
        total_constraint_violations = len(all_violations)
        
        # 电池电量统计
        final_battery_levels = [device.current_battery / device.max_battery 
                              for device in self.device_manager.devices]
        battery_depletion_count = sum(1 for device in self.device_manager.devices if device.is_depleted())
        
        # 设备统计
        device_stats = self.device_manager.get_system_statistics()
        server_stats = self.server_manager.get_system_statistics()
        
        return {
            'system_total_reward': float(system_total_reward),
            'system_mean_reward': float(system_mean_reward),
            'device_total_rewards': device_total_rewards.tolist(),
            'device_mean_rewards': device_mean_rewards.tolist(),
            'average_energy': final_system_metrics.get('average_energy', 0.0),
            'average_delay': final_system_metrics.get('average_delay', 0.0),
            'system_objective': final_system_metrics.get('system_objective', 0.0),
            'total_constraint_violations': total_constraint_violations,
            'final_battery_levels': final_battery_levels,
            'battery_depletion_count': battery_depletion_count,
            'device_stats': device_stats,
            'server_stats': server_stats,
            'episode_length': len(episode_rewards)
        }
    
    def seed(self, seed: int):
        """设置随机种子"""
        np.random.seed(seed)
    
    def get_environment_info(self) -> Dict:
        """获取环境信息"""
        return {
            'current_episode': self.current_episode,
            'current_step': self.current_step,
            'total_episodes': len(self.episode_history),
            'device_count': self.config.M,
            'server_count': self.config.N,
            'tasks_per_episode': self.config.tasks_per_episode
        }
    
    def print_environment_status(self):
        """打印环境状态摘要"""
        print("\n" + "="*60)
        print("环境状态摘要")
        print("="*60)
        
        env_info = self.get_environment_info()
        print(f"当前Episode: {env_info['current_episode']}")
        print(f"当前Step: {env_info['current_step']}")
        print(f"设备数量: {env_info['device_count']}")
        print(f"服务器数量: {env_info['server_count']}")
        
        # 打印设备和服务器状态
        self.device_manager.print_device_status()
        self.server_manager.print_server_status()
        
        print("="*60)
