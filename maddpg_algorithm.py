"""
MADDPG算法实现
包含经验回放缓冲区、MADDPG算法主体、智能体管理等核心功能
"""

import torch
import numpy as np
import random
from collections import deque
from typing import List, Dict, Tuple, Optional
from network_model import MADDPGAgent

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
    
    def push(self, experience: Tuple):
        """添加经验到缓冲区"""
        self.buffer.append(experience)
    
    def sample(self, batch_size: int, device: str = 'cpu') -> Dict[str, torch.Tensor]:
        """从缓冲区采样批次数据"""
        if len(self.buffer) < batch_size:
            return None
        
        batch = random.sample(self.buffer, batch_size)
        
        # 解包经验
        local_states, global_actions, rewards, next_local_states, dones = zip(*batch)
        
        # 转换为张量
        local_states = torch.FloatTensor(np.array(local_states)).to(device)
        global_actions = torch.FloatTensor(np.array(global_actions)).to(device)
        rewards = torch.FloatTensor(np.array(rewards)).to(device)
        next_local_states = torch.FloatTensor(np.array(next_local_states)).to(device)
        dones = torch.FloatTensor(np.array(dones)).to(device)
        
        return {
            'local_states': local_states,
            'global_actions': global_actions,
            'rewards': rewards,
            'next_local_states': next_local_states,
            'dones': dones
        }
    
    def __len__(self):
        return len(self.buffer)

class MADDPGAlgorithm:
    """MADDPG算法主体"""
    
    def __init__(self, config, device: str = 'cpu'):
        self.config = config
        self.device = device
        self.M = config.M  # 智能体数量
        self.N = config.N  # 服务器数量
        
        # 维度定义
        self.local_state_dim = 3      # [battery_ratio, frequency_ratio, absolute_battery]
        self.local_action_dim = 3     # [split_point_idx, frequency, server_idx]
        self.global_state_dim = self.M * self.local_state_dim + config.N * 4  # 设备状态 + 服务器状态
        self.global_action_dim = self.M * self.local_action_dim

        print(f"维度信息:")
        print(f"  本地状态维度: {self.local_state_dim}")
        print(f"  本地动作维度: {self.local_action_dim}")
        print(f"  全局状态维度: {self.global_state_dim}")
        print(f"  全局动作维度: {self.global_action_dim}")
        
        # 创建智能体
        self.agents = []
        for i in range(self.M):
            agent = MADDPGAgent(
                agent_id=i,
                state_dim=self.local_state_dim,
                action_dim=self.local_action_dim,
                global_state_dim=self.global_state_dim,
                global_action_dim=self.global_action_dim,
                config=config,
                device=device
            )
            self.agents.append(agent)
        
        # 经验回放缓冲区
        self.replay_buffer = ReplayBuffer(config.buffer_size)
        
        # 训练统计
        self.update_count = 0
        self.training_stats = {
            'actor_losses': [],
            'critic_losses': [],
            'noise_levels': []
        }
    
    def select_actions(self, local_states: np.ndarray, global_state: np.ndarray, 
                      add_noise: bool = True) -> Tuple[List[Dict], np.ndarray]:
        """选择所有智能体的动作"""
        actions = []
        global_action = []
        
        for i, agent in enumerate(self.agents):
            # 选择动作
            action_dict = agent.select_action(local_states[i], add_noise)
            actions.append(action_dict)
            
            # 构建全局动作向量
            agent_action = self._convert_action_dict_to_vector(action_dict)
            global_action.extend(agent_action)
        
        return actions, np.array(global_action)
    
    def _convert_action_dict_to_vector(self, action_dict: Dict) -> List[float]:
        """将动作字典转换为向量"""
        # 分割点：取argmax索引
        split_point_idx = np.argmax(action_dict['split_point'])
        
        # 频率：归一化值
        frequency = action_dict['frequency'].item() if hasattr(action_dict['frequency'], 'item') else action_dict['frequency']
        
        # 服务器：取argmax索引
        server_idx = np.argmax(action_dict['server'])
        
        return [float(split_point_idx), float(frequency), float(server_idx)]
    
    def store_experience(self, local_states: np.ndarray, global_state: np.ndarray, 
                        actions: List[Dict], global_action: np.ndarray, 
                        rewards: List[float], next_local_states: np.ndarray, 
                        next_global_state: np.ndarray, dones: List[bool]):
        """存储经验到回放缓冲区"""
        experience = (
            local_states.copy(),
            global_action.copy(),
            rewards.copy(),
            next_local_states.copy(),
            dones.copy()
        )
        
        self.replay_buffer.push(experience)
    
    def update_agents(self) -> Optional[Dict]:
        """更新所有智能体"""
        if len(self.replay_buffer) < self.config.batch_size:
            return None
        
        # 采样批次数据
        batch = self.replay_buffer.sample(self.config.batch_size, self.device)
        if batch is None:
            return None
        
        # 准备训练数据
        batch_data = self._prepare_batch_data(batch)
        
        actor_losses = []
        critic_losses = []
        
        # 更新每个智能体
        for agent in self.agents:
            # 更新Critic
            critic_loss = agent.update_critic(batch_data)
            critic_losses.append(critic_loss)
            
            # 更新Actor
            actor_loss = agent.update_actor(batch_data)
            actor_losses.append(actor_loss)
            
            # 软更新目标网络
            agent.soft_update()
        
        # 更新探索噪声
        for agent in self.agents:
            agent.update_noise()
        
        # 记录统计信息
        self.update_count += 1
        avg_actor_loss = np.mean(actor_losses)
        avg_critic_loss = np.mean(critic_losses)
        avg_noise = np.mean([agent.noise_std for agent in self.agents])
        
        self.training_stats['actor_losses'].append(avg_actor_loss)
        self.training_stats['critic_losses'].append(avg_critic_loss)
        self.training_stats['noise_levels'].append(avg_noise)
        
        return {
            'actor_loss': avg_actor_loss,
            'critic_loss': avg_critic_loss,
            'noise_level': avg_noise,
            'update_count': self.update_count
        }
    
    def _prepare_batch_data(self, batch: Dict) -> Dict:
        """准备批次训练数据"""
        local_states = batch['local_states']
        global_actions = batch['global_actions']
        rewards = batch['rewards']
        next_local_states = batch['next_local_states']
        dones = batch['dones']

        # 构建全局状态（包含设备状态和服务器状态）
        batch_size = local_states.shape[0]

        # 创建模拟的服务器状态（简化处理）
        server_states = torch.zeros(batch_size, self.N, 4).to(local_states.device)
        next_server_states = torch.zeros(batch_size, self.N, 4).to(local_states.device)

        # 构建完整的全局状态
        global_states = torch.cat([
            local_states.view(batch_size, -1),  # [batch_size, M*3]
            server_states.view(batch_size, -1)  # [batch_size, N*4]
        ], dim=-1)  # [batch_size, M*3 + N*4]

        next_global_states = torch.cat([
            next_local_states.view(batch_size, -1),
            next_server_states.view(batch_size, -1)
        ], dim=-1)

        # 为目标网络生成下一步动作
        next_actions_list = []
        for i, agent in enumerate(self.agents):
            with torch.no_grad():
                next_local_state = next_local_states[:, i, :]
                next_action_output = agent.target_actor(next_local_state)

                # 转换为向量形式
                split_point_idx = torch.argmax(next_action_output['split_point_probs'], dim=-1).float()
                frequency = next_action_output['frequency_normalized'].squeeze(-1)
                server_idx = torch.argmax(next_action_output['server_probs'], dim=-1).float()

                next_agent_action = torch.stack([split_point_idx, frequency, server_idx], dim=-1)
                next_actions_list.append(next_agent_action)

        next_global_actions = torch.cat(next_actions_list, dim=-1)

        return {
            'local_states': local_states,
            'states': global_states,
            'actions': global_actions,
            'rewards': rewards,
            'next_states': next_global_states,
            'next_actions': next_global_actions,
            'dones': dones
        }
    
    def set_train_mode(self):
        """设置所有智能体为训练模式"""
        for agent in self.agents:
            agent.set_train_mode()
    
    def set_eval_mode(self):
        """设置所有智能体为评估模式"""
        for agent in self.agents:
            agent.set_eval_mode()
    
    def save_models(self, save_dir: str, episode: int):
        """保存所有智能体的模型"""
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        for i, agent in enumerate(self.agents):
            filepath = os.path.join(save_dir, f'agent_{i}_episode_{episode}.pth')
            agent.save_models(filepath)
        
        # 保存算法状态
        algorithm_state = {
            'update_count': self.update_count,
            'training_stats': self.training_stats,
            'config': self.config.__dict__
        }
        
        algorithm_filepath = os.path.join(save_dir, f'algorithm_state_episode_{episode}.pth')
        torch.save(algorithm_state, algorithm_filepath)
    
    def load_models(self, save_dir: str, episode: int):
        """加载所有智能体的模型"""
        import os
        
        for i, agent in enumerate(self.agents):
            filepath = os.path.join(save_dir, f'agent_{i}_episode_{episode}.pth')
            if os.path.exists(filepath):
                agent.load_models(filepath)
            else:
                print(f"警告: 未找到智能体 {i} 的模型文件: {filepath}")
        
        # 加载算法状态
        algorithm_filepath = os.path.join(save_dir, f'algorithm_state_episode_{episode}.pth')
        if os.path.exists(algorithm_filepath):
            algorithm_state = torch.load(algorithm_filepath, map_location=self.device)
            self.update_count = algorithm_state.get('update_count', 0)
            self.training_stats = algorithm_state.get('training_stats', {
                'actor_losses': [],
                'critic_losses': [],
                'noise_levels': []
            })
    
    def get_training_statistics(self) -> Dict:
        """获取训练统计信息"""
        return {
            'update_count': self.update_count,
            'buffer_size': len(self.replay_buffer),
            'buffer_capacity': self.replay_buffer.capacity,
            'average_actor_loss': np.mean(self.training_stats['actor_losses'][-100:]) if self.training_stats['actor_losses'] else 0.0,
            'average_critic_loss': np.mean(self.training_stats['critic_losses'][-100:]) if self.training_stats['critic_losses'] else 0.0,
            'current_noise_level': np.mean([agent.noise_std for agent in self.agents]),
            'training_stats': self.training_stats
        }
    
    def reset_training_stats(self):
        """重置训练统计信息"""
        self.training_stats = {
            'actor_losses': [],
            'critic_losses': [],
            'noise_levels': []
        }
        self.update_count = 0
    
    def get_agent_info(self) -> List[Dict]:
        """获取所有智能体的信息"""
        agent_info = []
        for i, agent in enumerate(self.agents):
            info = {
                'agent_id': agent.agent_id,
                'noise_std': agent.noise_std,
                'training_mode': agent.training,
                'actor_params': sum(p.numel() for p in agent.actor.parameters()),
                'critic_params': sum(p.numel() for p in agent.critic.parameters())
            }
            agent_info.append(info)
        return agent_info
    
    def print_algorithm_status(self):
        """打印算法状态摘要"""
        print("\n" + "="*60)
        print("MADDPG算法状态摘要")
        print("="*60)
        
        stats = self.get_training_statistics()
        print(f"更新次数: {stats['update_count']}")
        print(f"缓冲区使用: {stats['buffer_size']}/{stats['buffer_capacity']}")
        print(f"平均Actor损失: {stats['average_actor_loss']:.6f}")
        print(f"平均Critic损失: {stats['average_critic_loss']:.6f}")
        print(f"当前噪声水平: {stats['current_noise_level']:.4f}")
        
        print(f"\n智能体信息:")
        for info in self.get_agent_info():
            print(f"  智能体 {info['agent_id']}: 噪声={info['noise_std']:.4f}, "
                  f"训练模式={info['training_mode']}")
        
        print("="*60)
