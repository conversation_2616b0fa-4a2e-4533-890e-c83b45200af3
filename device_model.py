"""
移动设备模型
包含设备状态管理、能耗计算、电池管理、自适应权重计算等功能
"""

import numpy as np
from typing import List, Dict, Tuple

class LocalDevice:
    """本地移动设备类"""
    
    def __init__(self, device_id: int, config):
        self.device_id = device_id
        self.config = config
        
        # 设备硬件参数（随机初始化，模拟异构性）
        self.max_battery = np.random.uniform(*config.max_battery_range)  # mAh
        self.current_battery = self.max_battery  # 初始电量满格
        self.min_battery = self.max_battery * config.B_min_ratio  # 最小电量阈值
        
        # CPU参数
        self.f_min = config.f_min
        self.f_max = config.f_max
        self.current_frequency = config.f_min  # 初始频率设为最小
        self.g_device = config.g_device
        
        # 能耗参数
        self.kappa = config.kappa
        self.P_static = np.random.uniform(*config.P_static_range)  # 静态功耗
        
        # 通信参数
        self.P_tx = np.random.uniform(*config.P_tx_range)  # 传输功率
        self.R_tx = np.random.uniform(*config.R_range)     # 传输速率
        
        # 状态记录
        self.energy_history = []
        self.frequency_history = []
        self.battery_history = []
    
    def get_state(self) -> np.ndarray:
        """获取设备状态向量 [battery_ratio, frequency_ratio, absolute_battery]"""
        battery_ratio = self.current_battery / self.max_battery
        frequency_ratio = (self.current_frequency - self.f_min) / (self.f_max - self.f_min)
        absolute_battery = self.current_battery / 1000.0  # 转换为Ah
        
        return np.array([battery_ratio, frequency_ratio, absolute_battery])
    
    def set_frequency(self, frequency: float):
        """设置CPU频率"""
        self.current_frequency = np.clip(frequency, self.f_min, self.f_max)
        self.frequency_history.append(self.current_frequency)
    
    def compute_local_processing_time(self, flops: float) -> float:
        """计算本地处理时间
        公式: T_local = C_local / (f_m * g_m)
        考虑电池状态对性能的影响
        """
        if flops <= 0:
            return 0

        # 电池电量影响处理性能
        battery_ratio = self.current_battery / self.max_battery
        if battery_ratio < 0.2:  # 电量低于20%时性能下降
            performance_factor = 0.7 + 0.3 * (battery_ratio / 0.2)
        else:
            performance_factor = 1.0

        effective_frequency = self.current_frequency * performance_factor
        return flops / (effective_frequency * self.g_device)
    
    def compute_communication_time(self, data_size: float) -> float:
        """计算通信传输时间
        公式: T_comm = D_tx / R_tx
        """
        if data_size <= 0:
            return 0
        return data_size / self.R_tx
    
    def compute_computation_energy(self, processing_time: float) -> float:
        """计算计算能耗
        公式: E_comp = κ * f³ * T_local (f单位GHz, T单位秒)
        """
        if processing_time <= 0:
            return 0
        frequency_ghz = self.current_frequency / 1e9
        return self.kappa * (frequency_ghz ** 3) * processing_time
    
    def compute_communication_energy(self, communication_time: float) -> float:
        """计算通信能耗
        公式: E_comm = P_tx * T_comm
        """
        if communication_time <= 0:
            return 0
        return self.P_tx * communication_time
    
    def compute_static_energy(self, total_time: float) -> float:
        """计算静态能耗
        公式: E_idle = P_static * T_total
        """
        if total_time <= 0:
            return 0
        return self.P_static * total_time
    
    def update_battery(self, energy_consumed: float):
        """更新电池电量
        转换公式: mAh = E(J) * 1000 / (V * 3600)
        """
        if energy_consumed > 0:
            # 将焦耳转换为mAh
            energy_mah = energy_consumed * 1000 / (self.config.voltage * 3600)
            self.current_battery = max(0, self.current_battery - energy_mah)
            
        self.energy_history.append(energy_consumed)
        self.battery_history.append(self.current_battery)
    
    def get_adaptive_weight(self) -> float:
        """计算自适应权重
        公式: α_m = 1 / (1 + exp(-β * (B_m/B_max - θ)))
        """
        battery_ratio = self.current_battery / self.max_battery
        exponent = -self.config.beta * (battery_ratio - self.config.theta)
        # 防止数值溢出
        exponent = np.clip(exponent, -500, 500)
        alpha = 1.0 / (1.0 + np.exp(exponent))
        return alpha
    
    def is_depleted(self) -> bool:
        """检查电池是否耗尽"""
        return self.current_battery <= self.min_battery
    
    def is_frequency_valid(self) -> bool:
        """检查频率是否在有效范围内"""
        return self.f_min <= self.current_frequency <= self.f_max
    
    def reset(self):
        """重置设备状态"""
        self.current_battery = self.max_battery
        self.current_frequency = self.f_min
        self.energy_history.clear()
        self.frequency_history.clear()
        self.battery_history.clear()
    
    def get_device_info(self) -> Dict:
        """获取设备完整信息"""
        return {
            'device_id': self.device_id,
            'max_battery': self.max_battery,
            'current_battery': self.current_battery,
            'battery_ratio': self.current_battery / self.max_battery,
            'current_frequency': self.current_frequency,
            'frequency_ghz': self.current_frequency / 1e9,
            'adaptive_weight': self.get_adaptive_weight(),
            'is_depleted': self.is_depleted(),
            'P_static': self.P_static,
            'P_tx': self.P_tx,
            'R_tx': self.R_tx / 1e6  # 转换为Mbps
        }
    
    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        return {
            'total_energy_consumed': sum(self.energy_history),
            'average_frequency': np.mean(self.frequency_history) if self.frequency_history else self.f_min,
            'battery_depletion_ratio': 1 - (self.current_battery / self.max_battery),
            'energy_efficiency': self.current_battery / self.max_battery if self.energy_history else 1.0
        }

class DeviceManager:
    """设备管理器"""
    
    def __init__(self, config):
        self.config = config
        self.devices = []
        self._initialize_devices()
    
    def _initialize_devices(self):
        """初始化所有设备"""
        for i in range(self.config.M):
            device = LocalDevice(i, self.config)
            self.devices.append(device)
    
    def get_all_states(self) -> np.ndarray:
        """获取所有设备状态矩阵 [M, 3]"""
        states = []
        for device in self.devices:
            states.append(device.get_state())
        return np.array(states)
    
    def get_device(self, device_id: int) -> LocalDevice:
        """获取指定设备"""
        if 0 <= device_id < len(self.devices):
            return self.devices[device_id]
        return None
    
    def update_all_batteries(self, energy_consumptions: List[float]):
        """批量更新所有设备电池"""
        for i, device in enumerate(self.devices):
            if i < len(energy_consumptions):
                device.update_battery(energy_consumptions[i])
    
    def set_all_frequencies(self, frequencies: List[float]):
        """批量设置所有设备频率"""
        for i, device in enumerate(self.devices):
            if i < len(frequencies):
                device.set_frequency(frequencies[i])
    
    def check_constraints(self) -> List[Dict]:
        """检查所有设备约束违反情况"""
        violations = []
        
        for device in self.devices:
            device_violations = []
            
            # 检查电池约束
            if device.is_depleted():
                device_violations.append({
                    'type': 'battery_depletion',
                    'device_id': device.device_id,
                    'current_battery': device.current_battery,
                    'min_battery': device.min_battery
                })
            
            # 检查频率约束
            if not device.is_frequency_valid():
                device_violations.append({
                    'type': 'frequency_violation',
                    'device_id': device.device_id,
                    'current_frequency': device.current_frequency,
                    'valid_range': [device.f_min, device.f_max]
                })
            
            violations.extend(device_violations)
        
        return violations
    
    def reset_all(self):
        """重置所有设备"""
        for device in self.devices:
            device.reset()
    
    def get_system_statistics(self) -> Dict:
        """获取系统级统计信息"""
        total_energy = sum(sum(device.energy_history) for device in self.devices)
        avg_battery_ratio = np.mean([device.current_battery / device.max_battery for device in self.devices])
        depleted_count = sum(1 for device in self.devices if device.is_depleted())
        
        adaptive_weights = [device.get_adaptive_weight() for device in self.devices]
        
        return {
            'total_energy_consumed': total_energy,
            'average_battery_ratio': avg_battery_ratio,
            'depleted_device_count': depleted_count,
            'total_devices': len(self.devices),
            'average_adaptive_weight': np.mean(adaptive_weights),
            'adaptive_weight_std': np.std(adaptive_weights),
            'constraint_violations': len(self.check_constraints())
        }
    
    def get_all_device_info(self) -> List[Dict]:
        """获取所有设备的详细信息"""
        return [device.get_device_info() for device in self.devices]
    
    def print_device_status(self):
        """打印设备状态摘要"""
        print("\n" + "="*60)
        print("设备状态摘要")
        print("="*60)
        
        for device in self.devices:
            info = device.get_device_info()
            print(f"设备 {info['device_id']}: "
                  f"电量 {info['battery_ratio']:.2%}, "
                  f"频率 {info['frequency_ghz']:.2f}GHz, "
                  f"权重 {info['adaptive_weight']:.3f}")
        
        stats = self.get_system_statistics()
        print(f"\n系统统计: 平均电量 {stats['average_battery_ratio']:.2%}, "
              f"耗尽设备 {stats['depleted_device_count']}/{stats['total_devices']}")
        print("="*60)
