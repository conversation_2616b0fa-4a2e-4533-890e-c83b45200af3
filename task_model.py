"""
推理任务模型
包含ResNet-50层级信息、早退出点管理、任务创建和工作负载分布计算
"""

import numpy as np
import uuid
from typing import Dict, List, Tuple, Optional

class ResNet50LayerInfo:
    """ResNet-50层级信息管理类"""
    
    def __init__(self):
        self.layers = self._load_layer_info()
        self.conv_layers = self._extract_conv_layers()
        self.exit_point_mapping = self._create_exit_point_mapping()
    
    def _load_layer_info(self) -> List[Dict]:
        """加载ResNet-50层级信息"""
        # 基于resnet50_cifar_analysis.txt的数据
        layers = []

        # 读取层级信息文件
        try:
            with open('resnet50_cifar_analysis.txt', 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        # 解析层级信息 - 格式: Layer X: Type, Input: [...], Output: [...], FLOPs: X, Params: X, Cumulative FLOPs: X
                        parts = line.split(', ')
                        if len(parts) < 5:
                            continue

                        # 提取层ID和类型
                        layer_part = parts[0]  # "Layer 0: Conv2d"
                        layer_id = int(layer_part.split(':')[0].replace('Layer ', ''))
                        layer_type = layer_part.split(':')[1].strip()

                        # 提取输入输出形状
                        input_shape = self._parse_shape(parts[1])  # "Input: [3, 32, 32]"
                        output_shape = self._parse_shape(parts[2])  # "Output: [64, 32, 32]"

                        # 提取FLOPs和参数
                        flops_part = parts[3]  # "FLOPs: 1769472.0"
                        params_part = parts[4]  # "Params: 1728"

                        flops = float(flops_part.split(':')[1].strip())
                        params = int(params_part.split(':')[1].strip())

                        # 提取累积FLOPs
                        if len(parts) >= 6:
                            cumulative_part = parts[5]  # "Cumulative FLOPs: 1769472.0"
                            cumulative_flops = float(cumulative_part.split(':')[1].strip())
                        else:
                            cumulative_flops = flops

                        # 计算输出数据大小 (bits)
                        if output_shape:
                            output_size = np.prod(output_shape) * 32  # 32位浮点数
                        else:
                            output_size = 0

                        layer_info = {
                            'layer_id': layer_id,
                            'layer_type': layer_type,
                            'input_shape': input_shape,
                            'output_shape': output_shape,
                            'flops': flops,
                            'params': params,
                            'cumulative_flops': cumulative_flops,
                            'output_size': output_size
                        }

                        layers.append(layer_info)

                    except (ValueError, IndexError) as e:
                        print(f"警告: 解析第{line_num+1}行时出错: {line}")
                        continue

        except FileNotFoundError:
            print("警告: 未找到resnet50_cifar_analysis.txt文件，使用默认层级信息")
            layers = self._get_default_layer_info()
        except Exception as e:
            print(f"警告: 读取层级信息文件时出错: {e}，使用默认层级信息")
            layers = self._get_default_layer_info()

        return layers
    
    def _parse_shape(self, shape_str: str) -> List[int]:
        """解析形状字符串"""
        try:
            # 提取方括号内的内容
            start = shape_str.find('[')
            end = shape_str.find(']')
            if start != -1 and end != -1:
                shape_content = shape_str[start+1:end]
                return [int(x.strip()) for x in shape_content.split(',')]
        except:
            pass
        return []
    
    def _extract_conv_layers(self) -> List[int]:
        """提取所有卷积层的ID"""
        conv_layers = []
        for layer in self.layers:
            if layer['layer_type'] == 'Conv2d':
                conv_layers.append(layer['layer_id'])
        return conv_layers
    
    def _create_exit_point_mapping(self) -> Dict[str, int]:
        """创建早退出点到最近卷积层的映射"""
        exit_points = {
            "Exit_1": 32,   # 对应最近的卷积层是Layer 30
            "Exit_2": 73,   # 需要找到最近的卷积层
            "Exit_3": 105,  # 需要找到最近的卷积层
            "Exit_4": 135,  # 需要找到最近的卷积层
            "Exit_5": 150,  # 需要找到最近的卷积层
            "Full": 164     # 对应最近的卷积层是Layer 162
        }
        
        mapping = {}
        for exit_name, exit_layer_id in exit_points.items():
            # 找到该退出点之前最近的卷积层
            nearest_conv = None
            for conv_layer_id in reversed(self.conv_layers):
                if conv_layer_id <= exit_layer_id:
                    nearest_conv = conv_layer_id
                    break
            
            if nearest_conv is not None:
                mapping[exit_name] = nearest_conv
            else:
                mapping[exit_name] = 0  # 默认为第一层
        
        return mapping
    
    def _get_default_layer_info(self) -> List[Dict]:
        """获取默认层级信息（备用）"""
        # 简化的默认层级信息
        default_layers = [
            {'layer_id': 0, 'layer_type': 'Conv2d', 'cumulative_flops': 1769472.0, 'output_size': 65536*32},
            {'layer_id': 30, 'layer_type': 'Conv2d', 'cumulative_flops': 221118464.0, 'output_size': 262144*32},
            {'layer_id': 68, 'layer_type': 'Conv2d', 'cumulative_flops': 771719168.0, 'output_size': 131072*32},
            {'layer_id': 100, 'layer_type': 'Conv2d', 'cumulative_flops': 1250350080.0, 'output_size': 65536*32},
            {'layer_id': 130, 'layer_type': 'Conv2d', 'cumulative_flops': 1464564736.0, 'output_size': 65536*32},
            {'layer_id': 148, 'layer_type': 'Conv2d', 'cumulative_flops': 1817068544.0, 'output_size': 8192*32},
            {'layer_id': 162, 'layer_type': 'Conv2d', 'cumulative_flops': 1942963200.0, 'output_size': 16384*32}
        ]
        return default_layers
    
    def get_layer_info(self, layer_id: int) -> Optional[Dict]:
        """获取指定层的信息"""
        for layer in self.layers:
            if layer['layer_id'] == layer_id:
                return layer
        return None
    
    def get_valid_split_points(self) -> List[int]:
        """获取所有有效的分割点（卷积层）"""
        return self.conv_layers.copy()
    
    def get_exit_point_conv_layer(self, exit_name: str) -> int:
        """获取早退出点对应的卷积层ID"""
        return self.exit_point_mapping.get(exit_name, 0)

class EdgeInferenceTask:
    """边缘推理任务类"""
    
    def __init__(self, task_id: str, accuracy_requirement: float, config):
        self.task_id = task_id
        self.accuracy_requirement = accuracy_requirement
        self.config = config
        
        # 决策变量
        self.split_point = None    # p_k: 分割点层ID
        self.exit_point = None     # e_k: 早退出点名称
        self.frequency = None      # f_k: 本地CPU频率
        self.target_server = None  # n_k: 目标服务器ID
        
        # 任务信息
        self.layer_info = ResNet50LayerInfo()
        self.selected_exit_point = self._select_exit_point()
        
    def _select_exit_point(self) -> str:
        """根据精度要求自动选择早退出点"""
        best_exit = "Full"
        min_accuracy_diff = float('inf')
        
        for exit_name, exit_info in self.config.exit_points.items():
            if exit_info['accuracy'] >= self.accuracy_requirement:
                accuracy_diff = exit_info['accuracy'] - self.accuracy_requirement
                if accuracy_diff < min_accuracy_diff:
                    min_accuracy_diff = accuracy_diff
                    best_exit = exit_name
        
        self.exit_point = best_exit
        return best_exit
    
    def set_decision_variables(self, split_point: int, frequency: float, target_server: int):
        """设置决策变量"""
        self.split_point = split_point
        self.frequency = frequency
        self.target_server = target_server
    
    def get_valid_split_points(self) -> List[int]:
        """获取当前任务的有效分割点"""
        all_conv_layers = self.layer_info.get_valid_split_points()
        exit_conv_layer = self.layer_info.get_exit_point_conv_layer(self.exit_point)
        
        # 只返回早退出点之前的卷积层
        valid_points = [0]  # 总是包含0（完全本地或完全卸载）
        for layer_id in all_conv_layers:
            if layer_id <= exit_conv_layer:
                valid_points.append(layer_id)
        
        return valid_points
    
    def compute_workload_distribution(self) -> Dict:
        """计算工作负载分布"""
        exit_info = self.config.exit_points[self.exit_point]
        total_flops = exit_info['cumulative_flops']
        exit_layer_id = self.layer_info.get_exit_point_conv_layer(self.exit_point)
        
        if self.split_point == 0:
            # 完全本地执行
            return {
                'local_flops': total_flops,
                'edge_flops': 0,
                'communication_data': 0,
                'execution_mode': 'local_only'
            }
        elif self.split_point >= exit_layer_id:
            # 完全卸载
            return {
                'local_flops': 0,
                'edge_flops': total_flops,
                'communication_data': 3 * 32 * 32 * 32,  # 输入数据大小(bits)
                'execution_mode': 'edge_only'
            }
        else:
            # 分割执行
            split_layer_info = self.layer_info.get_layer_info(self.split_point)
            if split_layer_info:
                local_flops = split_layer_info['cumulative_flops']
                edge_flops = total_flops - local_flops
                comm_data = split_layer_info['output_size']
            else:
                # 备用计算
                local_flops = total_flops * 0.3  # 假设30%本地计算
                edge_flops = total_flops * 0.7
                comm_data = 64 * 16 * 16 * 32  # 假设中间特征图大小
            
            return {
                'local_flops': local_flops,
                'edge_flops': edge_flops,
                'communication_data': comm_data,
                'execution_mode': 'split_execution'
            }
    
    def get_task_info(self) -> Dict:
        """获取任务完整信息"""
        workload = self.compute_workload_distribution()
        exit_info = self.config.exit_points[self.exit_point]
        
        return {
            'task_id': self.task_id,
            'accuracy_requirement': self.accuracy_requirement,
            'selected_exit_point': self.exit_point,
            'expected_accuracy': exit_info['accuracy'],
            'split_point': self.split_point,
            'frequency': self.frequency,
            'target_server': self.target_server,
            'workload_distribution': workload
        }

class TaskManager:
    """任务管理器"""
    
    def __init__(self, config):
        self.config = config
        self.active_tasks = []
        self.completed_tasks = []
    
    def create_task(self, accuracy_requirement: float) -> EdgeInferenceTask:
        """创建单个推理任务"""
        task_id = str(uuid.uuid4())
        task = EdgeInferenceTask(task_id, accuracy_requirement, self.config)
        return task
    
    def create_batch_tasks(self, num_tasks: int, accuracy_requirements: List[float]) -> List[EdgeInferenceTask]:
        """批量创建推理任务"""
        tasks = []
        for i in range(num_tasks):
            if i < len(accuracy_requirements):
                acc_req = accuracy_requirements[i]
            else:
                # 随机生成精度要求
                acc_req = np.random.uniform(*self.config.acc_min_range)
            
            task = self.create_task(acc_req)
            tasks.append(task)
        
        self.active_tasks.extend(tasks)
        return tasks
    
    def complete_task(self, task: EdgeInferenceTask):
        """完成任务处理"""
        if task in self.active_tasks:
            self.active_tasks.remove(task)
            self.completed_tasks.append(task)
    
    def reset(self):
        """重置任务管理器"""
        self.active_tasks.clear()
        self.completed_tasks.clear()
    
    def get_statistics(self) -> Dict:
        """获取任务统计信息"""
        return {
            'active_tasks': len(self.active_tasks),
            'completed_tasks': len(self.completed_tasks),
            'total_tasks': len(self.active_tasks) + len(self.completed_tasks)
        }
