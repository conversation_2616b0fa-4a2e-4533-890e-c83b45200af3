"""
系统配置参数
包含所有系统级别的配置参数，包括设备参数、服务器参数、任务参数、MADDPG算法参数等
"""

import numpy as np

class Config:
    def __init__(self):
        # ==================== 系统基础参数 ====================
        self.M = 4                    # 设备数量
        self.N = 4                    # 边缘服务器数量  
        self.delta_t = 0.15          # 全局时隙 150ms
        
        # ==================== 设备参数 ====================
        # 电池参数
        self.max_battery_range = [40, 60]  # 最大电池容量范围 (mAh)
        self.B_min_ratio = 0.05              # 最小电池电量比例 (5%)
        self.voltage = 5.0                   # 工作电压 (V)
        
        # 计算参数
        self.f_min = 0.3e9           # 最小CPU频率 (0.3 GHz)
        self.f_max = 1.2e9           # 最大CPU频率 (1.2 GHz)
        self.g_device = 153          # 设备每时钟周期浮点运算数 (FLOPs/cycle)
        
        # 能耗参数
        self.kappa = 1.3                    # 能耗系数 (W/GHz³)
        self.P_static_range = [3, 8]        # 静态功耗范围 (W)
        
        # 通信参数
        self.P_tx_range = [0.093, 0.115]    # 传输功率范围 (W)
        self.R_range = [45e6, 65e6]        # 传输速率范围 (bps)
        
        # ==================== 服务器参数 ====================
        self.f_server_range = [2.2e9, 2.8e9]  # 服务器频率范围 (Hz)
        self.g_server = 380                   # 服务器每时钟周期浮点运算数
        self.max_load = 0.75                  # 最大负载率 (75%)
        
        # ==================== 任务参数 ====================
        self.dataset = "cifar-10"             # 数据集
        self.model_name = "resnet50"          # 模型名称
        self.num_classes = 10                 # 分类数量
        self.acc_min_range = [0.70, 0.91]     # 精度要求范围
        self.T_max = 0.150                    # 最大延迟约束 (150ms)
        
        # ==================== 早退出点配置 ====================
        self.exit_points = {
            "Full": {"layer_id": 164, "accuracy": 0.9133, "cumulative_flops": 1942995968.0},
            "Exit_1": {"layer_id": 32, "accuracy": 0.77, "cumulative_flops": 221380608.0},
            "Exit_2": {"layer_id": 73, "accuracy": 0.8731, "cumulative_flops": 771855360.0},
            "Exit_3": {"layer_id": 105, "accuracy": 0.9038, "cumulative_flops": 1250425856.0},
            "Exit_4": {"layer_id": 135, "accuracy": 0.9113, "cumulative_flops": 1464640512.0},
            "Exit_5": {"layer_id": 150, "accuracy": 0.9121, "cumulative_flops": 1817076736.0}
        }
        
        # ==================== 自适应权重参数 ====================
        self.beta = 10.0    # 敏感度参数
        self.theta = 0.6    # 阈值参数 (60%电量时开始优先能耗)
        
        # ==================== MADDPG算法参数 ====================
        self.lr_actor = 1e-5              # Actor学习率
        self.lr_critic = 5e-5             # Critic学习率
        self.gamma = 0.99                 # 折扣因子
        self.tau = 0.005                  # 软更新系数
        self.buffer_size = 4000           # 经验回放缓冲区大小
        self.batch_size = 32              # 批次大小
        self.update_frequency = 2         # 更新频率
        self.warmup_steps = 100           # 预热步数
        
        # 噪声调度
        self.initial_noise = 0.2          # 初始噪声标准差
        self.final_noise = 0.05           # 最终噪声标准差
        self.noise_decay_ratio = 0.9      # 噪声衰减比例
        
        # 网络架构
        self.hidden_dims = [256, 128, 64] # 隐藏层维度
        self.dropout_rate = 0.1           # Dropout率
        self.grad_clip = 0.5              # 梯度裁剪阈值
        
        # ==================== 训练参数 ====================
        self.num_episodes = 2           # 训练轮数
        self.tasks_per_episode = 150      # 每轮任务数
        
        # ==================== 约束惩罚参数 ====================
        self.penalty_battery_depletion = 100    # 电池耗尽惩罚
        self.penalty_delay_violation = 100       # 延迟违反惩罚
        self.penalty_accuracy_violation = 200    # 精度违反惩罚
        self.penalty_server_overload = 100       # 服务器过载惩罚
        self.penalty_frequency_violation = 100   # 频率违反惩罚
        
    def get_adaptive_weight_formula(self):
        """返回自适应权重公式的描述"""
        return "α_m = 1 / (1 + exp(-β * (B_m/B_max - θ)))"
    
    def get_energy_formula(self):
        """返回能耗计算公式的描述"""
        return {
            "computation": "E_comp = κ * f³ * T_local",
            "communication": "E_comm = P_tx * T_comm", 
            "static": "E_idle = P_static * T_total",
            "total": "E_total = E_comp + E_comm + E_idle"
        }
    
    def get_delay_formula(self):
        """返回延迟计算公式的描述"""
        return {
            "local_processing": "T_local = C_local / (f_m * g_m)",
            "communication": "T_comm = D_tx / R_tx",
            "edge_processing": "T_edge = C_edge / (f_server * g_server)",
            "total_local": "T_total = T_local",
            "total_edge": "T_total = T_comm + T_edge", 
            "total_split": "T_total = T_local + T_comm + T_edge"
        }
    
    def validate_config(self):
        """验证配置参数的有效性"""
        assert self.M > 0, "设备数量必须大于0"
        assert self.N > 0, "服务器数量必须大于0"
        assert self.delta_t > 0, "时隙长度必须大于0"
        assert self.f_min < self.f_max, "最小频率必须小于最大频率"
        assert 0 < self.B_min_ratio < 1, "最小电池比例必须在0-1之间"
        assert self.beta > 0, "敏感度参数必须大于0"
        assert 0 < self.theta < 1, "阈值参数必须在0-1之间"
        assert 0 < self.tau < 1, "软更新系数必须在0-1之间"
        assert 0 < self.gamma <= 1, "折扣因子必须在0-1之间"
        print("配置参数验证通过")
        
    def print_config_summary(self):
        """打印配置摘要"""
        print("=" * 50)
        print("系统配置摘要")
        print("=" * 50)
        print(f"设备数量: {self.M}")
        print(f"服务器数量: {self.N}")
        print(f"时隙长度: {self.delta_t}s")
        print(f"CPU频率范围: {self.f_min/1e9:.1f}-{self.f_max/1e9:.1f} GHz")
        print(f"电池容量范围: {self.max_battery_range[0]}-{self.max_battery_range[1]} mAh")
        print(f"延迟约束: {self.T_max*1000:.0f}ms")
        print(f"训练轮数: {self.num_episodes}")
        print(f"每轮任务数: {self.tasks_per_episode}")
        print("=" * 50)
